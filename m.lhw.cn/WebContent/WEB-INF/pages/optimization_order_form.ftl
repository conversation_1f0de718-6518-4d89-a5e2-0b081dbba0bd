<#include "/common/common.ftl" />
<!DOCTYPE html>
<html>
<@head.head basePath="//${basePath}" title="填写订单">
<@gtm />
</@head.head>
<body onload="showMemberInfo(true)">

<@telCommon />
<div id="wrapper">
	<div id="datePlugin"></div>
	<div class="header-list cc ccx header">
    	<h3>填写表单</h3>
    	<a href="javascript:history.go(-1);" class="goback"></a>
		<a href="javascript:;" id="J_tel" class="tel cc">拨打电话</a>
    </div>  
<section id="container" class="clx">
	<form id="bookingFrom" action="${bookingPath}/booking/creditcard/${inDate}/${outDate}" method="post">
	<input type="hidden" name="roomNameEn" value="${room.roomNameEn?if_exists}"/>
	<input type="hidden" name="hotelcode" value="${hotel.hotelcode}" />
	<input type="hidden" name="sabrecode" value="${hotel.sabrecode}" />
	<input type="hidden" name="roomCode" value="${room.roomcode?if_exists}" />
	<input type="hidden" name="rateCode" value="${rate.rate_code?if_exists}" />
	<input type="hidden" name="indate" value="${ind?string("yyyyMMdd")}" />
	<input type="hidden" name="nights" value="${nights}" />
	<input type="hidden" name="roomRateCode" value="${rate.room_rate_code?if_exists}" />
	<input type="hidden" name="guaranteeType" value="${rate.guarantee_policy?if_exists}" />
	<input type="hidden" name="rooms" value="${param.rooms?if_exists}" />
	<input type="hidden" name="adultsPerRoom" value="${param.adultsPerRoom?if_exists}" />
	<input type="hidden" name="childrenPerRoom" value="${param.childrenPerRoom?if_exists}" />
	<input type="hidden" name="childrenAges" value="${param.childrenAges?if_exists}" />
	<input type="hidden" name="guest.salutation" value="<#if param.guest?exists>${param.guest.salutation?if_exists}</#if>"/>
	<input type="hidden" name="breakfastInclusive" value="${rate.breakfast_inclusive?if_exists}" />
	<#if rate.checkRule??>
	<input type="hidden" name="checkInAfter" value="${rate.checkRule.checkInAfter?if_exists}" />
	<input type="hidden" name="checkOutBefore" value="${rate.checkRule.checkOutBefore?if_exists}" />
	</#if>
	<input type="hidden" name="guaranteeRuleText" value="${rate.guaranteeRuleText?if_exists}" />
	<input type="hidden" name="cancelRuleText" value="${rate.cancelRuleText?if_exists}" />
	<input type="hidden" name="holdTimeText" value="${rate.holdTimeText?if_exists}" />
	
	<input type="hidden" name="roomTotal" value="${((rate.cny_estimated_total100) / 100)?string("#.##")}" />
	<input type="hidden" name="inDate" value="${inDate?if_exists}"/>
	<input type="hidden" name="outDate" value="${outDate?if_exists}"/>
	
	<input type="hidden" name="key" value="${key?if_exists}"/>
	
	<input type="hidden" name="roomName" value="${room.roomName?if_exists}"/>
	<input type="hidden" name="roomNameEn" value="${room.roomNameEn?if_exists}"/>
	<input type="hidden" name="rate_roomTotal" value="${((rate.total_rate100) / 100)?string("#.##")?if_exists}"/>
	<input type="hidden" name="rate_shuishou" value="${(((rate.estimated_total100)-(rate.total_rate100)) / 100)?string("#.##")?if_exists}"/>
	<input type="hidden" name="currency" value="${rate.currency?if_exists}"/>
	
	<input type="hidden" name="flag" value="${flag?if_exists}"/>
	
	<div class="tipsBox cc">
    	<p>${rate.cancelRuleText?if_exists}</p>
    </div><!--tipsBox end-->
	<div class="orderInfo">
    	<h3>${hotelName?if_exists}</h3>
		<p class="gray">${hotelNameEn?if_exists}</p>
		
		<#if hotel.countryCode?? && "CN" == hotel.countryCode>
			<#if hotel.hotelcode?? && "LW1825" == hotel.hotelcode>
				<h5 class="mt10">${room.roomName?if_exists}</h5>
				<p class="gray">${room.roomDesc?if_exists}</p>
            <#else>
            	<h5 class="mt10">${rate.room_description?if_exists}</h5>
				<p class="gray">${rate.room_text?if_exists}</p>
            </#if>
		</#if>
		<#if hotel.countryCode?? && "CN" != hotel.countryCode>
	    	<h5 class="mt10">${room.roomName?if_exists}</h5>
			<p class="gray">${room.roomDesc?if_exists}</p>
		</#if>
		
		<#if rate.breakfast_inclusive?? && "Y" == rate.breakfast_inclusive>
			<p class="gray">包含早餐</p>
		</#if>
		
		<#if hotel.countryCode?? && "CN" == hotel.countryCode>
			<#if hotel.hotelcode?? && "LW1825" == hotel.hotelcode>
				<p class="mt10">${rateDetailInfo.rateName?if_exists}&nbsp;&nbsp;&nbsp;&nbsp;${rateDetailInfo.rateDesc?if_exists}</p>
            <#else>
            	<p class="mt10">${rate.rate_description?if_exists}&nbsp;&nbsp;&nbsp;&nbsp;${rate.rate_text?if_exists}</p>
            </#if>
		</#if>
		<#if hotel.countryCode?? && "CN" != hotel.countryCode>
	    	<p class="mt10">${rateDetailInfo.rateName?if_exists}&nbsp;&nbsp;&nbsp;&nbsp;${rateDetailInfo.rateDesc?if_exists}</p>
		</#if>

        <P class="gray mt10">日期<span>${inMD}-${outMD}</span>共${nights?if_exists}晚</P>
        <a href="javascript:;" class="modify-select" style="font-size:14px; float: right; text-decoration:underline; font-weight:bold;">修改</a>
    	<p class="gray mt10">
            <em>房间数  ${param.rooms?if_exists}</em>
            <em>成人  ${param.adultsPerRoom?if_exists}</em>
            <em>儿童  ${param.childrenPerRoom?if_exists}</em>
       	</p>
       	
    </div><!--orderInfo end-->
	<h4>请按实际入住人填写，所填写姓名需要与入住时所持证件一致</h4>
    <ul class="formList">
        <li class="clx" id="J_form_chengwei">
        	<label>称谓</label>
        	<#if param.guest?exists>
        		<#if 1 == param.guest.salutation>
					<input type="text" class="inputCss" name="chengwei" value="先生 Mr." readonly="readonly" onfocus="this.blur();"/> 
        		</#if>
        		<#if 2 == param.guest.salutation>
					<input type="text" class="inputCss" name="chengwei" value="夫人 Mrs." readonly="readonly" onfocus="this.blur();"/> 
        		</#if>
        		<#if 3 == param.guest.salutation>
					<input type="text" class="inputCss" name="chengwei" value="女士 Ms." readonly="readonly" onfocus="this.blur();"/> 
        		</#if>
        		<#if 4 == param.guest.salutation>
					<input type="text" class="inputCss" name="chengwei" value="小姐 Miss." readonly="readonly" onfocus="this.blur();"/> 
        		</#if>
        	</#if>			
            <i></i>
        </li>
        <li class="clx">
        	<label>姓</label>
        	<input type="text" class="inputCss"  placeholder="请输入中文或英文" name="guest.lastNameCN" value="<#if param.guest?exists>${param.guest.lastNameCN?if_exists}</#if>" id="lastNameCN"/> 
     	</li>
        <li class="clx" style="background:#f4f4f4; height:2rem;">
        	<label style="line-height:2rem; background:#FFF;">&nbsp;</label>
        	<span style="padding:0 0.5rem"><input style="background:none; border:none;" type="text" class="" placeholder="" name="guest.lastName" value="<#if param.guest?exists>${param.guest.lastName?if_exists}</#if>" id="lastName" onkeyup="value=value.replace(/[^\w\s,]|_|[0-9]/ig,'')"/></span> 
     	</li>
        <li class="clx">
        	<label>名</label>
        	<input type="text" class="inputCss"  placeholder="请输入中文或英文" name="guest.firstNameCN" value="<#if param.guest?exists>${param.guest.firstNameCN?if_exists}</#if>" id="firstNameCN"/> 
      	</li>
    	<li class="clx" style="background:#f4f4f4; height:2rem;">
        	<label style="line-height:2rem; background:#FFF;">&nbsp;</label>
        	<span style="padding:0 0.5rem"><input style="background:none; border:none;" type="text" class="" placeholder="" name="guest.firstName" value="<#if param.guest?exists>${param.guest.firstName?if_exists}</#if>" id="firstName" onkeyup="value=value.replace(/[^\w\s,]|_|[0-9]/ig,'')"/></span>
      	</li>      
        <li class="clx ub">
        	<label style="display:inline-block; float:none;">手机</label>
        	<input type="text" class="inputCss l1" name="guest.countryCode" onblur="isNumber()" placeholder="+86" value="<#if param.guest?exists>${param.guest.countryCode?if_exists}</#if>" maxlength="5"/> 
        	<input type="number" name="guest.phone" class="inputCss l2 ub ub-f1 guest_phone"  placeholder="请输入手机号" value="<#if param.guest?exists>${param.guest.phone?if_exists}</#if>" id="phone"/> 
        </li>
    	<li class="clx">
        	<label>邮箱</label>
        	<input type="text" class="inputCss" onblur="" placeholder="订单确认后会给您发送邮件通知" name="guest.email" value="<#if param.guest?exists>${param.guest.email?if_exists}</#if>" id="email"/> 
      	</li>
    </ul><!--formList end-->
    
    <div class="other_demandto select-label fixed"  id="member_info" style="display:none;">
		<h4><span style="color:#B59E85;">贵为立鼎世尊享贵宾会员，此入住将尊享会员权益</span></h4>
	</div>
		
	<div class="other_demandto select-label fixed"  id="member_select" style="display:none;">
		<h4>
			<span style="color:#B59E85;">
				是否想获得立鼎世贵宾会一年免费会籍？
				<input type="radio" name="guestSelectFreeLC" value="Y"/><label for="type_1">是</label>
				<input type="radio" name="guestSelectFreeLC" value="N"/><label for="type_1">否</label>
			</span>
		</h4>
	</div>		
   
	<h4 class="J_zxgbhyy" id="J_zxgbhyy">
		<div class="tt" onclick="showAction()">
			<span class="underline" id="form-page-text-id">尊享贵宾会员</span>
			<i></i>
		</div>
		<div id="icon_q_box" style="display:none;">
			<p>如需使用免费房型升级，请拨打贵宾服务热线 <a style="font-size:14px;color:#9f8564;" href="tel: 400-1324-582">400-1324-582</a>
			， 或电邮<a style="font-size:14px;color:#9f8564;" href="mailto: <EMAIL>"><EMAIL></a></p>
		</div>
		<div id="icon_q_box_2" style="display:none;">
			<p>加入立鼎世尊享贵宾会，尊享众多权益及优惠获赠免费房晚。每次入住尊享免费欧式早餐、客房升级及延时退房等。</p>
			<a class="underline" href="//www.lhw.cn/leaders-club" target="view_window">了解更多</a>
		</div>
	</h4>
    <ul class="formList">
    	<li class="ubb clx" id="J_form_require">
        	<label style="width:auto;">入住要求</label>
			<input type="text" class="inputCss" value="无"  placeholder="无" readonly="readonly" onfocus="this.blur();"/> 
            <i></i>
            
            <div style="display:none;" id="J_rzyq_input">
			</div>
            
        </li>
        <!--<li class="clx">
        	<label style="width: auto;">房型升级</label>
        	<input id="upgrade_room" name="requested_service" type="text" value="9108" />
      	</li>-->
    </ul><!--formList end-->
    <h4>其他要求</h4>
	<div class="otherInfo">
		<textarea name="remarks" class="txtCss" placeholder="请使用英文填写，酒店将视情况尽量满足您的需求，若届时无法满足您的需求或产生额外费用，敬请谅解">${param.remarks?if_exists}</textarea>    	
    </div><!--otherInfo end-->
    
	<div class="hhh"></div>
	<div class="offerInfo dd clx">
    	<div class="leftBox left mt5">
        	<span >总房价</span>
        	<span class="price" >
            	<#if rate.currency != 'CNY'>
	            	<i style="padding-left:0.5rem;">CNY</i>
	            	<em style="font-size:1.15rem;font-weight:bold;">
	            	<#if rate.cny_estimated_total100?exists>
	            		<#if rate.cny_estimated_total100 % 100 ==0>
							${(rate.cny_estimated_total100 / 100)?string("#.00")}
						<#else>
							<#if rate.cny_estimated_total100 % 10 ==0>
								${(rate.cny_estimated_total100 / 100)?string("#.#'0'")}
							<#else>
								${(rate.cny_estimated_total100 / 100)?string("#.##")}
							</#if>
						</#if>
	            	</#if>
	            	</em>
	            	<span style="margin-right:0px;">&nbsp;&nbsp;</span><br/><span style="visibility:hidden">总房价</span>
            	</#if>
            	<i style="padding-left:0.5rem;">${rate.currency}</i>
            	<em style="font-size:1.15rem;">
            	<#if rate.estimated_total100?exists>
            		<#if rate.estimated_total100 % 100 ==0>
						${(rate.estimated_total100 / 100)?string("#.00")}
					<#else>
						<#if rate.estimated_total100 % 10 ==0>
							${(rate.estimated_total100 / 100)?string("#.#'0'")}
						<#else>
							${(rate.estimated_total100 / 100)?string("#.##")}
						</#if>
					</#if>
            	</#if>
            	</em>
            </span>
            <a href="javascript:;" class="mt5 details-policy">明细及政策</a>
        </div>
    	<div class="rightBox right clx">
            <a href="javascript:;" class="btn" id="creditcard_btn"><i>下一步</i><em>信用卡担保</em></a>
        </div>
    </div><!--offerInfo end-->
    </form>
</section><!--container end-->

<div class="mark" id="J_mark_chengwei">
	<div class="markBox">
        <ul class="markList" id="J_markList">
            <li class="cur" num="1"><i></i><span>先生 Mr.</span></li>
            <li num="2"><i></i><span>夫人 Mrs.</span></li>
            <li num="3"><i></i><span>女士 Ms.</span></li>
            <li num="4"><i></i><span>小姐 Miss.</span></li>
        </ul>
        <a class="btn" href="javascript:void(0);">确定</a>
    </div>
</div>

<div class="mark" id="J_mark_require">
	<div class="markBox">
		<p class="t14 white">最多可选择5项要求，酒店将视情况尽量满足您的要求，若届时无法满足活产生额外费用，敬请谅解。</p>
	        <ul class="markList cc" id="J_markList2">
	        	<!--<li id="upgrade_room" type="9108"><i></i><span>房型升级</span></li>-->
	            <li type="50137"><i></i><span>高楼层</span></li>
	            <li type="59004"><i></i><span>低楼层</span></li>
	            <li type="59001"><i></i><span>远离电梯</span></li>
	            <li type="74"><i></i><span>无烟客房</span></li>
				<li type="101"><i></i><span>可吸烟客房</span></li>
				<li type="22"><i></i><span>连通客房</span></li>
				<li type="1"><i></i><span>毗邻客房</span></li>
				<!--<li type="QR"><i></i><span>安静客房</span></li>
				<li type="AL"><i></i><span>无过敏源客房</span></li>-->
				<li type="221"><i></i><span>无障碍设施客房</span></li>
				<li type="9115"><i></i><span>公园景观</span></li>
				<!--<li type="PO"><i></i><span>泳池景观</span></li>-->
				<li type="9116"><i></i><span>滨水景观</span></li>
				<li type="9112"><i></i><span>城市景观</span></li>
				<li type="7"><i></i><span>阳台/露台</span></li>
				<!--<li type="BD"><i></i><span>生日</span></li>
				<li type="HM"><i></i><span>度蜜月夫妇</span></li>
				<li type="AV"><i></i><span>周年纪念日</span></li>-->
				<li type="135"><i></i><span>海绵枕</span></li>
				<li type="187"><i></i><span>非致敏枕</span></li>
				<li type="132"><i></i><span>羽绒枕</span></li>
				<!--<li type="RG"><i></i><span>曾入住过</span></li>-->
        </ul><!--markList end-->
        <a class="btn" href="javascript:">确定</a>
    </div><!--markBox end-->
</div><!--mark end-->
</div><!--wrapper end-->

<div class="opacty_layer"></div>

<div class="dialog" style="display:none;">
	<div class="dialog-centent" style="color:black;">您选择的房型或价格不能预定，请返回后重新选择</div>
	<div class="dialog-btn"><p class="trueBtn">确定</p></div>
</div>

<div class="pop_div" style="display:none;">
	<div class="pop_layer" style="">
		<h4>${room.roomName?if_exists} <span>${param.rooms?if_exists}</span>间     <#if rate.breakfast_inclusive?exists && "Y" == rate.breakfast_inclusive>包含早餐<#else>不含早餐</#if></h4>
		<div class="pop_list fixed">
			<!-- <ul>
				<li class="fixed"> -->
				<table style="width:90%;margin:0 auto;">
					<tr>
						<td colspan=3><p class="text_fr" style="width:140px;">房&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;费：</p></td>
					</tr>
					<#list dates as date>
					<#if rate.currency != 'CNY'>
					<tr>
						<td>
						<!--p class="rate_list" style="margin-left:0px;margin-right:10px;float:right;"-->
						<em style="float:left;">${date.dateStr}&nbsp;&nbsp;&nbsp;</em>
						</td>
						<td>
							<em style="float:left;">
							CNY&nbsp;</em>
						</td>
						<td>
							<em style="float:right;">
								<#if date.cnyRate100 % 100 ==0>
									${(date.cnyRate100 / 100)?string("#.00")}
								<#else>
									<#if date.cnyRate100 % 10 ==0>
										${(date.cnyRate100 / 100)?string("#.#'0'")}
									<#else>
										${(date.cnyRate100 / 100)?string("#.##")}
									</#if>
								</#if></em>
						</td>
					</tr>
					</#if>
					<tr>
						<td><em style="<#if rate.currency != 'CNY'>visibility:hidden;</#if>float:left;">${date.dateStr}&nbsp;&nbsp;&nbsp;</em>
						</td>
						<td>
						<em style="float:left;">${rate.currency}&nbsp;</em>
						</td>
						<td>
						<em style="float:right;">
						<#if date.rate100 % 100 ==0>
							${(date.rate100 / 100)?string("#.00")}
						<#else>
							<#if date.rate100 % 10 ==0>
								${(date.rate100 / 100)?string("#.#'0'")}
							<#else>
								${(date.rate100 / 100)?string("#.##")}
							</#if>
						</#if>
						</em>
						</td>
					<!-- </p> -->
					<tr>
					</#list>
					<!--<p><em>${rate.currency}<#if rate.totalRate100?exists>${((rate.totalRate100) / 100)?string("#.##")}</#if></em></p>-->
			<!-- 	</li>
				<li class="fixed"> -->
				<tr>
					<td colspan=3><p class="text_fr" style="font-style:normal;color:#999;width:140px;">税收及服务费：</p></td>
				</tr>
				<#if rate.currency !='CNY'>
				<tr>
					<td></td>
					<!-- <p style="margin-left:0px;margin-right:10px;float:right;"> -->
					<td>	<em style="">CNY&nbsp;</em></td>
					<td>	<em style="float:right;">
						<#if rate.cny_total_rate100?exists && rate.cny_estimated_total100?exists>
							<#if rate.cny_estimated_total100 == rate.cny_total_rate100>0.00
							<#elseif ((rate.cny_estimated_total100)-(rate.cny_total_rate100)) % 100 ==0>
								${(((rate.cny_estimated_total100)-(rate.cny_total_rate100)) / 100)?string("#.00")}
							<#else>
								<#if ((rate.cny_estimated_total100)-(rate.cny_total_rate100)) % 10 ==0>
									${(((rate.cny_estimated_total100)-(rate.cny_total_rate100)) / 100)?string("#.#'0'")}
								<#else>
									${(((rate.cny_estimated_total100)-(rate.cny_total_rate100)) / 100)?string("#.##")}
								</#if>
							</#if>
						</#if>
						</em>
					</td>
				</tr>
				</#if>
				<tr>
					<td></td>
					<td><em style="float:left;">${rate.currency}&nbsp;</em></td>
					<td>
						<em style="float:right;">
						<#if rate.total_rate100?exists && rate.estimated_total100?exists>
							<#if rate.cny_estimated_total100 == rate.cny_total_rate100>0.00
							<#elseif ((rate.estimated_total100)-(rate.total_rate100)) % 100 ==0>
								${(((rate.estimated_total100)-(rate.total_rate100)) / 100)?string("#.00")}
							<#else>
								<#if ((rate.estimated_total100)-(rate.total_rate100)) % 10 ==0>
									${(((rate.estimated_total100)-(rate.total_rate100)) / 100)?string("#.#'0'")}
								<#else>
									${(((rate.estimated_total100)-(rate.total_rate100)) / 100)?string("#.##")}
								</#if>
							</#if>
						</#if>
						</em>
					</td>
				</tr>
				<!--	</p>
				 </li>
				<li class="fixed"> -->
				<tr>
					<td colspan=3>
						<p class="text_fr" style="font-style:normal;color:#999;width:140px;">总房费：</p>
					</td>
				</tr>
				<#if rate.currency?? && 'CNY' !=rate.currency>
				<tr>
					<td/>
					<td><!-- <p style="margin-left:0px;margin-right:10px;float:right;"> -->
							<em style="">CNY&nbsp;</em>
					</td>
					<td>
						<em style="float:right;">
						<#if rate.cny_estimated_total100?exists>
							<#if rate.cny_estimated_total100 % 100 ==0>
								${(rate.cny_estimated_total100 / 100)?string("#.00")}
							<#else>
								<#if rate.cny_estimated_total100 % 10 ==0>
									${(rate.cny_estimated_total100 / 100)?string("#.#'0'")}
								<#else>
									${(rate.cny_estimated_total100 / 100)?string("#.##")}
								</#if>
							</#if>
						</#if>
						</em>&nbsp;&nbsp;
					</td>
				</tr>
				</#if>
				<tr>
					<td></td>
					<td>
					<em>${rate.currency}&nbsp;</em>
					</td>
					<td>
					<em style="float:right;">
					<#if rate.estimated_total100?exists>
						<#if rate.estimated_total100 % 100 ==0>
							${(rate.estimated_total100 / 100)?string("#.00")}
						<#else>
							<#if rate.estimated_total100 % 10 ==0>
								${(rate.estimated_total100 / 100)?string("#.#'0'")}
							<#else>
								${(rate.estimated_total100 / 100)?string("#.##")}
							</#if>
						</#if>
					</#if>
					</em>
					</td>
				</tr>
			</table>
				<!--	</p>
				 </li>
			</ul> -->
		</div>
		<br/>
		<p>您将根据付款当天的汇率以酒店当地的货币付费给酒店</p>
		<p>已包含服务费 此为参考价，最终房价将根据付 款当天汇率以酒店当地货币支付</p>
		<#if rate.cancelRuleText?? && rate.cancelRuleText != "">
		<br/>
		<p><b>取消政策：</b>${rate.cancelRuleText?if_exists}</p>
		</#if>
		<#if rate.guaranteeRuleText?? && rate.guaranteeRuleText != "">
		<br/>
		<p><b>担保政策：</b>${rate.guaranteeRuleText?if_exists}</p>
		</#if>
		<p><button type="button" class="closeBtn">关闭</button></p>
	</div>
</div>

<div class="pop_modify_order">
	<h4>您即将返回选择界面进行重新选择，此页面未保存的信息将丢失</h4>
	<div class="select-btns">
		<a href="javascript:;" class="btn cancelBtn">取消</a>
		<a href="//${basePath}/m/hotel/${hotel.nameEnCode}/${hotel.sabrecode}?inDate=${inDate}&outDate=${outDate}" class="btn">确定</a>	
	</div>
</div>

<form id="historyForm" method="post">
	<input type="hidden" name="inDate" value="${inDate}" />
	<input type="hidden" name="outDate" value="${outDate}" />
	<input type="hidden" name="numadult" value="${numadult?if_exists}" />
	<input type="hidden" name="numchild" value="${numchild?if_exists}" />
	<input type="hidden" name="membersJsonStr" value="${membersJsonStr?if_exists}" />
</form>
<@script.script basePath="//${basePath}">
<script type="text/javascript">

var reg = /^[_a-zA-Z0-9_-_._-]+@([_a-zA-Z0-9_-]+\.)+[a-zA-Z]{2,3}$/;
var reg2 = /[^\x00-\xff]/g;

var guestLastNameCNHistory = "<#if param.guest?exists>${param.guest.lastNameCN?if_exists}</#if>" ;
var guestFirstNameCNHistory = "<#if param.guest?exists>${param.guest.firstNameCN?if_exists}</#if>" ;

function isNumber(){
	var countryCode = $("#bookingFrom input[name='guest.countryCode']").val();
	var reg = /^(\+?)\d{1,4}$/;
	if(!reg.test(countryCode)){
		$("#bookingFrom input[name = 'guest.countryCode']").val('');
		return false;
	}else{
		return true;
	}
}

$(function() {

	var $form = $('#bookingFrom');
	
	var _flag = $("#bookingFrom input[name='flag']").val();
	
	if(_flag == 'no'){

		$(".opacty_layer, .dialog").show();
	}
	
	$(".trueBtn").click(function(){
		window.history.go(-1);
	});
	
	$('.details-policy').click(function() {
		$('body').eq(0).css('overflow','hidden');
		common.pop(".pop_div");
		common.ckhide({"classly":".opacty_layer","hidedv":".pop_div"});
		common.ckhide({"classly":".opacty_layer","hidedv":".popselect"});
	});
	
	$('.modify-select').click(function(event){
		event.preventDefault();
		$('body').eq(0).css('overflow','hidden');
		common.pop(".pop_modify_order");
		common.ckhide({"classly":".opacty_layer","hidedv":".pop_modify_order"});
		common.ckhide({"classly":".opacty_layer","hidedv":".popselect"});		
	})
	$('.pop_modify_order .cancelBtn').click(function(event){
		event.preventDefault();
		$('body').eq(0).css('overflow','auto');
		$(".pop_modify_order").hide();
		$(".opacty_layer").hide();
		$(".popselect").hide();
	});	
	
	var $aphone = $('#areaCodePhone'), $phone = $('#phone');
	
	$('#creditcard_btn').click(function() {
		var bl = true;
		var obj = null ;
		var params = $form.serializeArray();
		for (var i = 0; i < params.length; i++) {
			obj = params[i], val = obj.value;
			if (!val) {
				if ('guest.lastNameCN' == obj.name) {
					bl = false;
					common.alertPop('请输入姓氏！');
					break;
				} else if ('guest.firstNameCN' == obj.name) {
					bl = false;
					common.alertPop('请输入名字！');
					break;
				}else if ('guest.lastName' == obj.name) {
					bl = false;
					common.alertPop('请输入姓氏拼音！');
					break;
				} else if ('guest.firstName' == obj.name) {
					bl = false;
					common.alertPop('请输入名字拼音！');
					break;
				} else if ('guest.countryCode' == obj.name) {
					bl = false;
					common.alertPop('请输入手机国家编码！');
					break;
				} else if ('guest.phone' == obj.name) {
					bl = false;
					common.alertPop('请输入手机号！');
					break;
				} else if ('guest.email' == obj.name) {
					bl = false;
					common.alertPop('请输入Email！');
					break;
				}				
			} else {
			 	if ('guest.lastName' == obj.name && val == '') {
			 		// /[^A-Za-z\s,]/.test(val)
					bl = false;
					common.alertPop('姓氏拼音请用拼音输入！');
					break;
				} else if ('guest.firstName' == obj.name &&  val == '') {
					// [^A-Za-z\s,]/.test(val)
					bl = false;
					common.alertPop('名字拼音请用拼音输入！');
					break;
				}else if ('guest.countryCode' == obj.name && !/^(\+?)\d{1,4}$/.test(val)) {
					bl = false;
					common.alertPop('请输入正确的手机号国家编码！');
					break;
				} else if ('guest.phone' == obj.name && val.replace(/\s+/g, "").length <= 6) {
					bl = false;
					//common.alertPop('请输入正确的手机号！');
					common.alertPop('手机号不得少于6位！');				
					break;
				} else if ('guest.email' == obj.name && !reg.test(val)) {
					bl = false;
					common.alertPop('请输入正确的 Email！');
					break;
				} else if ('remarks' == obj.name && reg2.test(val)) {
					bl = false;
					common.alertPop('其他需求，请用英文填写！');
					break;
				}
			}
		}
		
		if (isShowGuestSelectFreeLC==false && isRunhowGuestSelectFreeLC==false) {
				showMemberInfo(false,false);
			}
	
		if (bl && isShowGuestSelectFreeLC ) {
			var isGuestSelectedInput = $("#bookingFrom input[name='guestSelectFreeLC']") ;
			if(isGuestSelectedInput.filter(":checked")==null || isGuestSelectedInput.filter(":checked").length==0){
					bl = false;
					common.alertPop('请选择“是否想获得立鼎世贵宾会一年免费会籍？”');
			} 
		}
			
		
		if (bl) {
			var rateJson = ${rateJson}
			var startTime = $("#bookingFrom input[name=inDate]").val().split('-')[2];
			var endTime = $("#bookingFrom input[name=outDate]").val().split('-')[2];
			var time =  parseInt(endTime) - parseInt(startTime);
			dataLayer.push({
				'event':'checkout',
				'eventCategory': 'Checkout',
				'eventAction':'Checkout2',
				'eventLabel':'Next step payment',
				'dimension7':$('#bookingFrom input[name=inDate]').val() + '::' + $('#bookingFrom input[name=outDate]').val(),
				'ecommerce':{
					'checkout':{
						'actionField':{'step':2, 'option':''},
						'products':[{
							'id':'${hotel.hotelcode}',
							'name':`${hotel.hotelNameEn}`,
							'category':'',
							'brand':'',
							'variant':$('#bookingFrom input[name=roomCode]').val() + '::' + '${room.roomNameEn}',
							'price': (rateJson.cny_rate100 / 100) * time,
							'quantity':1
						}]
					},
				}
			})
			// $(".opacty_layer").show();
			FH.dispatchForm("${lhwTraceOrderNext}",$form,{hotelName:"${hotel.hotelNameEn}",cityName:"${hotel.cityNameEn}"});
			$form.attr('action','${bookingPath}/cc-guarantee');
			$form.submit();
		}
		
	});
	
	$(document).ready(function(){
		checkLogin();
	});
	
	function checkLogin() {
		if($.cookie(islogin_cookie) != "") {
	        var uuid = $.cookie(uuid_cookie); 
	        $.post(
	       		"/m/checkLogin",
	       		{
	       			"uuid" : uuid
	       		},
	       		function(data) {
		   			if (data.isLogind == "1") {
		   				var memberInfo = data.memberInfoResult;
						$("#score_is").text("积分：");
				 		$("#hello").text("你好");
						$("#memberScore").text(memberInfo.memberScore);
						$("#memberName").text(memberInfo.firstName);
						$("#roomUpgraded").text(memberInfo.roomUpgraded);
						$("#isLogind").val(data.isLogind);
//		 				Club、Sterling & Aurelian
						switch(memberInfo.memberLevel) {
							case "Club":
								$("#levelC").show();
							  break;
							case "Sterling":
								$("#levelS").show();
							  break;
							case "Aurelian":
								$("#levelA").show();
								$("#J_zxgbhyy").hide();
							  break;
						}
						setCookies('', '', data);
						if (memberInfo.renew == "Expired") {
							$.cookie(islogin_cookie, 2, {expires:date, path:'/', domain:'lhw.cn'});
						} else {
							var curruntPage = window.location.href;
							if (curruntPage.indexOf('/m/form') > 1) {
								memberOrderAutofills(data);
			            	}
						}
		   			} else {
		   				memberOrderAutofill();
		   			}
	       	}).error(function(){

	       	}); 
	    }
	}
	
	function memberOrderAutofills(data){
		var memberInfo = data.memberInfoResult;
		if (data.isLogind == "1") {
			var prefix = memberInfo.prefix;
			var guestLastNameCN = memberInfo.lastName;
			var guestFirstNameCN = memberInfo.firstName;
			$("#form-page-text-id").text("使用客房升级");
			if(guestLastNameCN){
				$("#lastNameCN").val(guestLastNameCN);
				$("#lastNameCN").attr("readonly","readonly");
				$("#lastName").val(getGuestNamePinyin(guestLastNameCN));
				
			}
			if(guestFirstNameCN){
				$("#firstNameCN").val(guestFirstNameCN);
				$("#firstNameCN").attr("readonly","readonly");
				$("#firstName").val(getGuestNamePinyin(guestFirstNameCN));
				
			}
			$("#lastName").attr("readonly","readonly");
			$("#firstName").attr("readonly","readonly");
			$("#email").val(data.username);
			$("#email").attr("readonly","readonly");
			$("#phone").val(memberInfo.phoneNumber);
			
			
			
			var prefixs = new Array()
			prefixs[0] = "先生 Mr.";
			prefixs[1] = "夫人 Mrs.";
			prefixs[2] = "女士 Ms.";
			prefixs[3] = "小姐 Miss";
			for (var prefix1 in prefixs) {
				if (prefixs[prefix1].indexOf(prefix) != -1 ) {
					$("input[name='chengwei']").val(prefixs[prefix1]);
				}
			}
			$('#icon_q_box_2').hide();
		} else {
			$("#email").removeAttr("readonly");
			$("#email").removeAttr("background-color");
		}
	}
	
	function memberOrderAutofill(){
		var islogin_cookie = "lhw.cn_islogin_m";
		if ($.cookie(islogin_cookie) == "1") {
			var identify_cookie = "lhw.cn_identify_m";
			var first_name_cookie = "lhw.cn_first_name_m";
			var last_name_cookie = "lhw.cn_last_name_m";
			var prefix_cookie = "lhw.cn_prefix_m";
			var phone_cookie = "lhw.cn_phone_m";
			var prefix = $.cookie(prefix_cookie);
			var guestLastNameCN = $.cookie(last_name_cookie);
			var guestFirstNameCN = $.cookie(first_name_cookie);
			if(guestLastNameCN){
				$("#lastNameCN").val(guestLastNameCN);
				$("#lastNameCN").attr("readonly","readonly");
			}
			
// 			$("#lastNameCN").css("background-color","#e6e6e6");
			if(guestFirstNameCN){
				$("#firstNameCN").val(guestFirstNameCN);
				$("#firstNameCN").attr("readonly","readonly");
			}
			
// 			$("#firstNameCN").css("background-color","#e6e6e6");
			$("#email").val($.cookie(identify_cookie));
			$("#email").attr("readonly","readonly");
// 			$("#email").css("background-color","#e6e6e6");
			$("#phone").val($.cookie(phone_cookie));
			$("#lastName").val(getGuestNamePinyin(guestLastNameCN));
			$("#lastName").attr("readonly","readonly");
// 			$("#lastName").parent().css("background-color","#e6e6e6").css("width", "232px");
// 			$("#lastName").parent().parent().css('background','');
			$("#firstName").val(getGuestNamePinyin(guestFirstNameCN));
			$("#firstName").attr("readonly","readonly");
// 			$("#firstName").parent().css("background-color","#e6e6e6").css("width", "232px");
// 			$("#firstName").parent().parent().css('background','');
			var prefixs = new Array()
			prefixs[0] = "先生 Mr.";
			prefixs[1] = "夫人 Mrs.";
			prefixs[2] = "女士 Ms.";
			prefixs[3] = "小姐 Miss";
			for (var prefix1 in prefixs) {
				if (prefixs[prefix1].indexOf(prefix) != -1 ) {
					$(".inputCss").html(prefixs[prefix1]);
// 					$(".inputCss").attr("readonly","readonly");
// 					$(".inputCss").css("background-color","#e6e6e6");
				}
			}
			$('#icon_q_box_2').hide();
		} else {
			$("#email").removeAttr("readonly");
			$("#email").removeAttr("background-color");
		}
	}
	
	$('.child-num').click(function() {
		$(".opacty_layer,.child-num-select").show();
	});
	
	$('.prev').click(function() {
		var $hform = $('#historyForm');
		$hform.attr('action','//${basePath}/m/roomlist');
		$hform.submit();
	});
	
	$('.change-per').click(function() {
		//$aphone.val('+'+ $areaCode.val() +'-'+ $phone.val());
		$form.attr('action','${bookingPath}/change');
		$form.submit();
	});
	$('.closeBtn').click(function(){
		$('body').eq(0).css('overflow','auto');
		$(".pop_div").hide();
		$(".opacty_layer").hide();
		$(".popselect").hide();
	});
	
	
	$("#lastNameCN").blur(function(){
			var guestLastNameCN = $(this).val() ;
			if(guestLastNameCNHistory != guestLastNameCN){		
				var pinyin = getGuestNamePinyin(guestLastNameCN) ;
				//if(pinyin!=null&&pinyin.length>0){
					$("#lastName").val(pinyin) ;
					//bookingFrom.guest.lastName.setCustomValidity('');
				//}
				guestLastNameCNHistory = guestLastNameCN ;
			}
	});
	
	$("#firstNameCN").blur(function(){
			var guestFirstNameCN = $(this).val() ;
			if(guestFirstNameCNHistory != guestFirstNameCN){		
				var pinyin = getGuestNamePinyin(guestFirstNameCN) ;
				//if(pinyin!=null&&pinyin.length>0){
					$("#firstName").val(pinyin) ;
					//bookingFrom.guest.firstName.setCustomValidity('');
				//}
				guestFirstNameCNHistory = guestFirstNameCN ;
			}
	});
	
	$(".requested_service").find("label").click(function(){
		var requested_service_num = 0;
		$("#bookingFrom input[name=requested_service]").each(function(){
			if($(this).is(':checked')){
				requested_service_num = requested_service_num + 1 ;			
			}
		});
		if(requested_service_num>5){
			$(this).find("input").removeAttr("checked");
			// alert("最多只能选5项");
		}
	});	
	
	
});

var flag = true;
function showAction(){
	var islogin = $.cookie(islogin_cookie);
	if(flag){
		// $('#arrowImg').attr('src', '//${basePath}/static/img/arrow2.jpg');
		// $(this).css("background-image","url(on.jpg)");
		if (islogin == '1') {
			$('#icon_q_box').css("display","block");
		} else {
			$('#icon_q_box_2').css("display","block");
		}
		$('.J_zxgbhyy i').addClass('show');
		flag = false;
		
		
	}else{
		// $('#arrowImg').attr('src', '//${basePath}/static/img/arrow1.jpg');
		if (islogin == '1') {
			$('#icon_q_box').css("display","none");
		} else {
			$('#icon_q_box_2').css("display","none");
		}
		$('.J_zxgbhyy i').removeClass('show');
		flag = true;
	}
}

function isGuestMember(guestEmail,inDate,async){
	if(async==null){
		async = true ;
	}
	var url="/m/resvguest/?guestEmail="+guestEmail + "&inDate="+inDate; 
	return $.ajax({
		url:url,
		async:async
		});
}

var isShowGuestSelectFreeLC = false ;
var isRunhowGuestSelectFreeLC = false ;
function showMemberInfo(isInit,async){
	
	isShowGuestSelectFreeLC = false ;
	
	var guestEmailValue = $("#bookingFrom input[name='guest.email']").val() ;
	
	if(isInit && (guestEmailValue==null || guestEmailValue=='')){
		return false;
	}		
					
	if (!reg.test(guestEmailValue)){
		$('#member_info').hide();
		$('#member_select').hide();
		$('#member_info_not').show();
		isRunhowGuestSelectFreeLC = false ;
		// common.alertPop('请输入正确的 Email！');
		return false;
	}else{			
		$.when(
			 isGuestMember(guestEmailValue,$("#bookingFrom input[name='inDate']").val(),async)
			).then(function(data) {
				if(data!=null){
					if(data.isMember){
						$('#member_info').show();
						$('#member_info_not').hide();$('#icon_q_box').hide();
						$('#member_select').hide();
					}else{
						$('#member_info').hide();
						if(data.isGuestFirstResv&&data.isCheckInDateMoreLate){
							/**此活动20160901下线
							$('#member_select').show();
							isShowGuestSelectFreeLC = true ;
							$('#member_info_not').hide();$('#icon_q_box').hide();**/
						}else{
							$('#member_select').hide();
							$('#member_info_not').show();
						}
					}
				}
				isRunhowGuestSelectFreeLC = true ;
		}, function(err) {
			//$(".wrap").remove();  
		});
		
	}
}
//房型升级显示隐藏
var loginstate = $.cookie("lhw.cn_islogin_m");
if(loginstate){
	$("#upgrade_room").css("display","");
}else{
	$("#upgrade_room").css("display","none");
}
var hotel = '${rateJson}';
</script>
</@script.script>
</body>
</html>
<!-- /pages/optimization_order_form.ftl -->