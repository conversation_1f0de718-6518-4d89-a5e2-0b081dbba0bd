package net.hubs1.lhw.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.aliyun.captcha20230305.Client;
import com.aliyun.captcha20230305.models.VerifyIntelligentCaptchaRequest;
import com.aliyun.captcha20230305.models.VerifyIntelligentCaptchaResponse;
import com.aliyun.teaopenapi.models.Config;
import net.hubs1.lhw.api.Config as LhwConfig;

/**
 * 阿里云验证码2.0服务工具类
 * 用于验证前端传来的验证码参数
 */
@Component
public class AliyunCaptcha2Util {
    
    private static final Logger logger = LoggerFactory.getLogger(AliyunCaptcha2Util.class);
    
    // 阿里云验证码2.0配置参数
    private String accessKeyId;
    private String accessKeySecret;
    private String endpoint;
    private String regionId;
    
    private Client client;
    
    /**
     * 初始化阿里云验证码2.0客户端
     */
    private void initClient() {
        if (client == null) {
            try {
                // 从统一配置类获取配置参数
                loadConfig();

                Config config = new Config()
                    .setAccessKeyId(accessKeyId)
                    .setAccessKeySecret(accessKeySecret)
                    .setEndpoint(endpoint);

                client = new Client(config);
                logger.info("阿里云验证码2.0客户端初始化成功，endpoint: {}", endpoint);
            } catch (Exception e) {
                logger.error("阿里云验证码2.0客户端初始化失败", e);
            }
        }
    }

    /**
     * 从统一配置类加载配置参数
     */
    private void loadConfig() {
        try {
            accessKeyId = LhwConfig.getProperty("aliyun.captcha.accessKeyId", "");
            accessKeySecret = LhwConfig.getProperty("aliyun.captcha.accessKeySecret", "");
            endpoint = LhwConfig.getProperty("aliyun.captcha.endpoint", "captcha.cn-shanghai.aliyuncs.com");
            regionId = LhwConfig.getProperty("aliyun.captcha.regionId", "cn-shanghai");

            logger.debug("加载验证码2.0配置: endpoint={}, regionId={}", endpoint, regionId);
        } catch (Exception e) {
            logger.error("加载验证码2.0配置失败", e);
            // 设置默认值
            accessKeyId = "";
            accessKeySecret = "";
            endpoint = "captcha.cn-shanghai.aliyuncs.com";
            regionId = "cn-shanghai";
        }
    }
    
    /**
     * 验证阿里云验证码2.0
     * 
     * @param captchaVerifyParam 验证码验证参数，由前端验证码组件回调提供
     * @param sceneId 场景ID（可选，建议传入）
     * @return 验证结果对象
     */
    public CaptchaVerifyResult verifyCaptcha(String captchaVerifyParam, String sceneId) {
        CaptchaVerifyResult result = new CaptchaVerifyResult();
        
        // 检查必要参数
        if (isEmpty(captchaVerifyParam)) {
            logger.warn("验证码参数为空: captchaVerifyParam={}", captchaVerifyParam);
            result.setSuccess(false);
            result.setMessage("验证码参数不能为空");
            result.setVerifyCode("F002");
            return result;
        }
        
        // 检查配置参数
        if (isEmpty(accessKeyId) || isEmpty(accessKeySecret)) {
            logger.error("阿里云验证码配置参数不完整");
            result.setSuccess(false);
            result.setMessage("验证码服务配置错误");
            return result;
        }
        
        try {
            // 初始化客户端
            initClient();
            
            if (client == null) {
                logger.error("阿里云验证码2.0客户端初始化失败");
                result.setSuccess(false);
                result.setMessage("验证码服务初始化失败");
                return result;
            }
            
            // 创建验证请求
            VerifyIntelligentCaptchaRequest request = new VerifyIntelligentCaptchaRequest()
                .setCaptchaVerifyParam(captchaVerifyParam);
            
            // 如果提供了场景ID，则设置
            if (!isEmpty(sceneId)) {
                request.setSceneId(sceneId);
            }
            
            logger.info("开始验证阿里云验证码2.0: sceneId={}", sceneId);
            
            // 调用验证接口
            VerifyIntelligentCaptchaResponse response = client.verifyIntelligentCaptcha(request);
            
            if (response != null && response.getBody() != null) {
                Boolean success = response.getBody().getSuccess();
                String code = response.getBody().getCode();
                String message = response.getBody().getMessage();
                String verifyCode = null;
                Boolean verifyResult = false;
                
                if (response.getBody().getResult() != null) {
                    verifyResult = response.getBody().getResult().getVerifyResult();
                    verifyCode = response.getBody().getResult().getVerifyCode();
                }
                
                logger.info("阿里云验证码2.0验证结果: success={}, code={}, message={}, verifyResult={}, verifyCode={}", 
                           success, code, message, verifyResult, verifyCode);
                
                result.setSuccess(Boolean.TRUE.equals(success) && Boolean.TRUE.equals(verifyResult));
                result.setMessage(message);
                result.setCode(code);
                result.setVerifyCode(verifyCode);
                result.setVerifyResult(verifyResult);
                
                if (result.isSuccess()) {
                    logger.info("验证码2.0验证通过");
                } else {
                    logger.warn("验证码2.0验证失败: verifyCode={}, message={}", verifyCode, message);
                }
                
            } else {
                logger.error("阿里云验证码2.0验证响应为空");
                result.setSuccess(false);
                result.setMessage("验证码服务响应异常");
            }
            
        } catch (Exception e) {
            logger.error("阿里云验证码2.0验证异常", e);
            result.setSuccess(false);
            result.setMessage("验证码验证异常: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查字符串是否为空
     */
    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 获取客户端真实IP地址
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    public static String getClientIpAddress(javax.servlet.http.HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        String xRealIp = request.getHeader("X-Real-IP");
        String remoteAddr = request.getRemoteAddr();
        
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            // X-Forwarded-For可能包含多个IP，取第一个
            return xForwardedFor.split(",")[0].trim();
        }
        
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return remoteAddr;
    }
    
    /**
     * 验证码验证结果类
     */
    public static class CaptchaVerifyResult {
        private boolean success;
        private String message;
        private String code;
        private String verifyCode;
        private Boolean verifyResult;
        
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public String getCode() {
            return code;
        }
        
        public void setCode(String code) {
            this.code = code;
        }
        
        public String getVerifyCode() {
            return verifyCode;
        }
        
        public void setVerifyCode(String verifyCode) {
            this.verifyCode = verifyCode;
        }
        
        public Boolean getVerifyResult() {
            return verifyResult;
        }
        
        public void setVerifyResult(Boolean verifyResult) {
            this.verifyResult = verifyResult;
        }
        
        @Override
        public String toString() {
            return "CaptchaVerifyResult{" +
                    "success=" + success +
                    ", message='" + message + '\'' +
                    ", code='" + code + '\'' +
                    ", verifyCode='" + verifyCode + '\'' +
                    ", verifyResult=" + verifyResult +
                    '}';
        }
    }
}
