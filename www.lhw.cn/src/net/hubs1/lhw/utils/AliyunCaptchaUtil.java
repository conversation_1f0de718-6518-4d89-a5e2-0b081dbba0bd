package net.hubs1.lhw.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigRequest;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.aliyun.tea.*;

/**
 * 阿里云验证码服务工具类
 * 用于验证前端传来的验证码参数
 */
@Component
public class AliyunCaptchaUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(AliyunCaptchaUtil.class);
    
    // 阿里云验证码配置参数，可以通过配置文件注入
    @Value("${aliyun.captcha.accessKeyId:}")
    private String accessKeyId;
    
    @Value("${aliyun.captcha.accessKeySecret:}")
    private String accessKeySecret;
    
    @Value("${aliyun.captcha.appKey:}")
    private String appKey;
    
    @Value("${aliyun.captcha.scene:}")
    private String scene;
    
    @Value("${aliyun.captcha.regionId:cn-hangzhou}")
    private String regionId;
    
    private IAcsClient client;
    
    /**
     * 初始化阿里云客户端
     */
    private void initClient() {
        if (client == null) {
            try {
                IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
                client = new DefaultAcsClient(profile);
                DefaultProfile.addEndpoint(regionId, regionId, "afs", "afs.aliyuncs.com");
                logger.info("阿里云验证码客户端初始化成功");
            } catch (Exception e) {
                logger.error("阿里云验证码客户端初始化失败", e);
            }
        }
    }
    
    /**
     * 验证阿里云验证码
     * 
     * @param sessionId 会话ID，从前端success回调中获取
     * @param sig 签名串，从前端success回调中获取
     * @param token 请求唯一标识，从前端success回调中获取
     * @param remoteIp 客户端IP地址
     * @return true表示验证通过，false表示验证失败
     */
    public boolean verifyCaptcha(String sessionId, String sig, String token, String remoteIp) {
        // 检查必要参数
        if (isEmpty(sessionId) || isEmpty(sig) || isEmpty(token) || isEmpty(remoteIp)) {
            logger.warn("验证码参数不完整: sessionId={}, sig={}, token={}, remoteIp={}", 
                       sessionId, sig, token, remoteIp);
            return false;
        }
        
        // 检查配置参数
        if (isEmpty(accessKeyId) || isEmpty(accessKeySecret) || isEmpty(appKey) || isEmpty(scene)) {
            logger.error("阿里云验证码配置参数不完整");
            return false;
        }
        
        try {
            // 初始化客户端
            initClient();
            
            if (client == null) {
                logger.error("阿里云验证码客户端初始化失败");
                return false;
            }
            
            // 创建验证请求
            AuthenticateSigRequest request = new AuthenticateSigRequest();
            request.setSessionId(sessionId);
            request.setSig(sig);
            request.setToken(token);
            request.setScene(scene);
            request.setAppKey(appKey);
            request.setRemoteIp(remoteIp);
            
            logger.info("开始验证阿里云验证码: sessionId={}, token={}, remoteIp={}", sessionId, token, remoteIp);
            
            // 调用验证接口
            AuthenticateSigResponse response = client.getAcsResponse(request);
            
            if (response != null) {
                int code = response.getCode();
                logger.info("阿里云验证码验证结果: code={}, msg={}", code, response.getMsg());
                
                // 100表示验签通过，900表示验签失败
                if (code == 100) {
                    logger.info("验证码验证通过");
                    return true;
                } else {
                    logger.warn("验证码验证失败: code={}, msg={}", code, response.getMsg());
                    return false;
                }
            } else {
                logger.error("阿里云验证码验证响应为空");
                return false;
            }
            
        } catch (Exception e) {
            logger.error("阿里云验证码验证异常", e);
            return false;
        }
    }
    
    /**
     * 检查字符串是否为空
     */
    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 获取客户端真实IP地址
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    public static String getClientIpAddress(javax.servlet.http.HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        String xRealIp = request.getHeader("X-Real-IP");
        String remoteAddr = request.getRemoteAddr();
        
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            // X-Forwarded-For可能包含多个IP，取第一个
            return xForwardedFor.split(",")[0].trim();
        }
        
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return remoteAddr;
    }
}
