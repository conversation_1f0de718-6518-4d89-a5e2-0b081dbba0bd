package net.hubs1.lhw.constants;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import cn.lhw.cms.entity.User;

public class Constant {
	
	public static final String LOGIN_SESSION = "login_session";
	public static final String SESSION_USER = "suser";
	public static final String CHARTSET_UTF8 = "UTF-8";
	public static final String NOTLOGIN_CODE = "100";
	public static final String SUCCESS_CODE = "1";
	public static final String FAIL_CODE = "0";
	public static final String PRODUCES_UTF8 = "text/html;charset=UTF-8";
	public static final int DEFAULT_PAGE_LIMIT = 10;
	
	public static boolean isNotLogin(HttpServletRequest request) {
		return !isLogin(request);
	}
	
	public static boolean isLogin(HttpServletRequest request) {
		HttpSession session = request.getSession();
		Object sobj = session.getAttribute(LOGIN_SESSION);
		if(null == sobj) {
			return false;
		}
		User suser = (User) sobj;
		if(null!=suser && suser.getIsLogind()==1) {
			return true;
		}
		return false;
	}
	
	public static User getLogin(HttpServletRequest request) {
		HttpSession session = request.getSession();
		Object sobj = session.getAttribute(LOGIN_SESSION);
		if(null == sobj) {
			return null;
		}else {
			return (User) sobj;
		}
	}

}
