package net.hubs1.lhw.model;

public class MemberInfoResult {
	
	private String code;	//登录结果
	
	private String uuid;	//会员uuid
	
	private String memberLevel;	//会员等级 Club、Sterling & Aurelian
	
	private String memberScore;// 会员积分
	
	private String firstName;// 名字
	
	private String lastName;// 姓
	
	private String email;// 会员邮箱
	
	private String phoneNumber;// 会员手机号码
	
	private String roomUpgraded;// 房型可升级次数
	
	private String prefix;//称呼
	
	private String memberNo;//会员编号
	
	private String renew; //会员续费状态
	
	private String expirationdate;

	public String getPrefix() {
		return prefix;
	}

	public void setPrefix(String prefix) {
		this.prefix = prefix;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMemberLevel() {
		return memberLevel;
	}

	public void setMemberLevel(String memberLevel) {
		this.memberLevel = memberLevel;
	}

	public String getMemberScore() {
		return memberScore;
	}

	public void setMemberScore(String memberScore) {
		this.memberScore = memberScore;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public String getRoomUpgraded() {
		return roomUpgraded;
	}

	public void setRoomUpgraded(String roomUpgraded) {
		this.roomUpgraded = roomUpgraded;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getMemberNo() {
		return memberNo;
	}

	public void setMemberNo(String memberNo) {
		this.memberNo = memberNo;
	}

	public String getRenew() {
		return renew;
	}

	public void setRenew(String renew) {
		this.renew = renew;
	}

	public String getExpirationdate() {
		return expirationdate;
	}

	public void setExpirationdate(String expirationdate) {
		this.expirationdate = expirationdate;
	}
}
