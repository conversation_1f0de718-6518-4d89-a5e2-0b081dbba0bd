package net.hubs1.lhw.action;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import com.google.gson.Gson;

import cn.lhw.cms.api.HotelSearchApi;
import cn.lhw.cms.api.LhwCmsApi;
import cn.lhw.cms.api.domain.MenuItemDomain;

@Controller
public class Header {

	private Logger logger = LoggerFactory.getLogger(this.getClass());
	
	@Autowired
	HotelSearchApi search;
	
	@Autowired
	LhwCmsApi cms;
	
	final static Gson gson = new Gson();
	
	@RequestMapping(value="/header")
	public ModelAndView header1() {
		ModelAndView mv= new ModelAndView();
		mv.setViewName("header");
		return mv;
	}

}
