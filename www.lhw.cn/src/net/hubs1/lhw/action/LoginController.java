package net.hubs1.lhw.action;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import net.hubs1.lhw.constants.Constant;
import net.hubs1.lhw.constants.MemberApiResult;
import net.hubs1.lhw.model.MemberActivityResult;
import net.hubs1.lhw.model.MemberInfoResult;
import net.hubs1.lhw.model.User;
import net.hubs1.lhw.redis.RedisService;
import net.hubs1.lhw.service.LoginService;
import net.hubs1.lhw.utils.AliyunCaptchaUtil;

@Controller
public class LoginController {
	
	private Logger logger = LoggerFactory.getLogger(LoginController.class);
	
	@Autowired
	private LoginService loginService;
	
	@Autowired
	RedisService redisService;
	
	@RequestMapping(value="mlogin", method=RequestMethod.POST)
	public @ResponseBody User login(HttpServletRequest request,
			HttpServletResponse response, @RequestParam String email, @RequestParam String password,
			@RequestParam(required = false) String captcha, @RequestParam(required = false) String captchaId) {
		HttpSession session = request.getSession();
		Cookie cookie = new Cookie("JSESSIONID", session.getId());
		cookie.setPath("/");
		response.addCookie(cookie);
		
		User user = new User();
		
		if (StringUtils.isEmpty(email) || StringUtils.isEmpty(password)) {
			// 服务端参数非空验证
			MemberInfoResult memberInfoResult = new MemberInfoResult();
			memberInfoResult.setCode(MemberApiResult.FAIL.getCode());
			user.setMemberInfoResult(memberInfoResult);
			return user;
		}
		email = email.trim();
		password = password.trim();
		MemberInfoResult memberInfoResult = loginService.login(email, password);
		if (memberInfoResult != null) {
			logger.info("登陆信息memberInfoResult", memberInfoResult);
			user.setMemberInfoResult(memberInfoResult);
			if (MemberApiResult.SUCCESS.getCode().equals(memberInfoResult.getCode())) {
				// 登录成功
				user.setIsLogind(1);
				user.setUsername(email);
				user.setUsername(memberInfoResult.getEmail());
				user.setSessionId(session.getId());
				session.setAttribute(Constant.LOGIN_SESSION, user);
			}
		} else {
			logger.info("登陆失败", email);
			// 登录异常
			memberInfoResult = new MemberInfoResult();
			memberInfoResult.setCode(MemberApiResult.FAIL.getCode());
			user.setMemberInfoResult(memberInfoResult);
		}
		return user;
	}
	
	@RequestMapping(value="qrlogin", method=RequestMethod.POST)
	public @ResponseBody User qrlogin(HttpSession session, HttpServletRequest request,
			HttpServletResponse response, @RequestParam String uuidId) {
		session.invalidate();
		session = request.getSession();
		Cookie cookie = new Cookie("JSESSIONID", session.getId());
		cookie.setPath("/");
		response.addCookie(cookie);
		
		User user = new User();
		MemberInfoResult memberInfoResult = loginService.qrlogin(uuidId);
		if (memberInfoResult != null) {
			if (MemberApiResult.SUCCESS.getCode().equals(memberInfoResult.getCode())) {
				// 获取到微信扫码登录结果
				user.setIsLogind(1);
				user.setUsername(memberInfoResult.getEmail());
				user.setSessionId(session.getId());
				session.setAttribute(Constant.LOGIN_SESSION, user);
				user.setMemberInfoResult(memberInfoResult);
			} else {
				// 获取到微信扫码登录结果
				user.setIsLogind(1);
				memberInfoResult = new MemberInfoResult();
				memberInfoResult.setCode(MemberApiResult.FAIL.getCode());
				user.setMemberInfoResult(memberInfoResult);
			}
			redisService.delete("qr_"+uuidId);
		} else {
			// 没有获取到微信扫码登录结果
			user.setIsLogind(-1);
			memberInfoResult = new MemberInfoResult();
			memberInfoResult.setCode(MemberApiResult.FAIL.getCode());
			user.setMemberInfoResult(memberInfoResult);
		}
		return user;
	}
	
	
	@RequestMapping(value="checkLogin", method=RequestMethod.POST)
	public @ResponseBody User checkLogin(HttpSession session, HttpServletRequest request,
			HttpServletResponse response, @RequestParam String uuid) {
		User user = new User();
		if (StringUtils.isEmpty(uuid)) {
			// 说明cookie失效，返回登录状态isLogin是0，用户可以重新登录。
			return user;
		}
		uuid = uuid.trim();
		MemberInfoResult memberInfoResult = loginService.getUserInfo(uuid);
		if (memberInfoResult != null) {
			if (MemberApiResult.SUCCESS.getCode().equals(memberInfoResult.getCode())) {
				//登录成功
				user.setIsLogind(1);
				user.setUsername(memberInfoResult.getEmail());
				user.setMemberInfoResult(memberInfoResult);
				user.setSessionId(session.getId());
				session.setAttribute(Constant.LOGIN_SESSION, user);
			}
		} else {
			// 登录异常
			memberInfoResult = new MemberInfoResult();
			memberInfoResult.setCode(MemberApiResult.FAIL.getCode());
			user.setMemberInfoResult(memberInfoResult);
		}
		return user;
	}
	
	
	@RequestMapping(value="partakeActivity", method=RequestMethod.POST)
	public @ResponseBody Map<String, String> partakeActivity(HttpSession session, HttpServletRequest request,
			HttpServletResponse response, @RequestParam String uuid, @RequestParam String actId) {
		Map<String, String> map = loginService.partakeActivity(uuid, actId);
		if (map == null) {
			map = new HashMap<>();
			map.put("code", MemberApiResult.FAIL.getCode());
		}
		return map;
	}
	
	@RequestMapping(value="promotion", method=RequestMethod.POST)
	public @ResponseBody User promotion(HttpSession session, HttpServletRequest request,
			HttpServletResponse response, @RequestParam String uuid) {
		User user = new User();
		try {
			if (!StringUtils.isEmpty(uuid)) {
				MemberActivityResult memberActivityResult = loginService.getActivity(uuid);
				if (memberActivityResult != null) {
					user.setIsPromotion(1);
					user.setMemberActivityResult(memberActivityResult);
				}
			} else {
				logger.error("促销活动获取失败， 登录返回的uuid是空，uuid=" + uuid);
			}
		} catch (Exception e) {
			logger.error("uuid=" + uuid + "的促销活动获取失败", e);
		}
		return user;
	}
	
	@RequestMapping(value="/vaildate/tokenKey/{tokenKey}/token/{token}",method=RequestMethod.GET)
	public ModelAndView validateToken(@PathVariable("tokenKey") String tokenKey,@PathVariable("token") String token) {
		ModelAndView mv= new ModelAndView();
		String email = loginService.validateToken(tokenKey, token);
		System.out.println(email);
		if(null!=email&&!"".equals(email)) {
			mv.addObject("email", email);
			mv.setViewName("reset_password");
		}else {
			mv.setViewName("reset_password_false");
		}
		return mv;
	}
	
	@RequestMapping(value="/reset/email/password",method=RequestMethod.POST)
	public ModelAndView resetPassword(String email,String password) {
		ModelAndView mv= new ModelAndView();
		boolean isSuccess = loginService.resetPassword(email, password);
		System.out.println(isSuccess);
		if(isSuccess) {
			mv.setViewName("reset_password_success");
		}else {
			mv.setViewName("reset_password_false");
		}
		return mv;
	}
	
	@RequestMapping(value="/forgetPassword", method=RequestMethod.GET)
	public @ResponseBody String forgetPassword(HttpSession session, HttpServletRequest request,
			HttpServletResponse response) {
		String result = "false";
		String email = request.getParameter("email");
		Map<String,String[]> paramMap = request.getParameterMap();
		logger.error(Arrays.toString(paramMap.get("email")) + "忘记密码邮件发送-1");

		logger.error("email=" + email + "忘记密码邮件发送1");
		try {
			if (!StringUtils.isEmpty(email)) {
				if("true".equals(loginService.checkIsCorrectEmail(email))) {
					if("true".equals(loginService.forgetPassword(email))){
						result = "success";
					}else {
						result = "forget-false";
					}
				}else {
					result = "check-false";
				}
			}
			logger.error("email=" + email + "忘记密码邮件发送");
		} catch (Exception e) {
			logger.error("email=" + email + "忘记密码邮件发送方法发生异常", e);
		}
		return result;
	}

	@RequestMapping(value = "/pre-product-auth", method = RequestMethod.GET)
	public String checkMode(HttpServletResponse response, @RequestParam(required = false) String secretCode,
			@RequestParam(required = false) String logout) {
		Boolean result = null;
		String cookieVal = null;
		// 关闭预生产
		if (!StringUtils.isEmpty(logout) && "1".equals(logout)) {
			result = false;
		} else {
			// 打开预生产
			if (!StringUtils.isEmpty(secretCode) && "dragontrail".equals(secretCode)) {
				result = true;
				cookieVal = secretCode;
			}
		}
		if (result != null) {
			Cookie cookie = new Cookie("pre-product-auth", cookieVal);
			cookie.setPath("/");
			cookie.setDomain(".lhw.cn");
			response.addCookie(cookie);
		}
		return "/pre-product-auth";
	}
}
