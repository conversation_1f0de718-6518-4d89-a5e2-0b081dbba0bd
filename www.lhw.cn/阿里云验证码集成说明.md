# 阿里云验证码服务集成说明

## 概述
本文档说明如何在LoginController中集成阿里云验证码服务，增强登录安全性。

## 已完成的工作

### 1. 创建了阿里云验证码工具类
- 文件位置：`src/net/hubs1/lhw/utils/AliyunCaptchaUtil.java`
- 功能：封装阿里云验证码验证逻辑
- 支持获取客户端真实IP地址

### 2. 修改了LoginController
- 文件位置：`src/net/hubs1/lhw/action/LoginController.java`
- 在`mlogin`方法中添加了验证码验证逻辑
- 新增了`verifyCaptcha`接口用于独立验证验证码

### 3. 添加了配置参数
- 文件位置：`src/config.properties`
- 添加了阿里云验证码相关配置项

## 需要手动完成的工作

### 1. 添加阿里云验证码SDK依赖
需要将以下JAR文件添加到`WebContent/WEB-INF/lib/`目录：

- `aliyun-java-sdk-core-4.5.13.jar` 或更高版本
- `aliyun-java-sdk-afs-1.0.1.jar` 或更高版本

可以从以下地址下载：
- Maven中央仓库：https://mvnrepository.com/artifact/com.aliyun
- 阿里云官方SDK：https://help.aliyun.com/zh/captcha/

### 2. 配置阿里云验证码参数
在`config.properties`文件中，需要替换以下配置项的实际值：

```properties
# 需要替换为实际的应用标识
aliyun.captcha.appKey=YOUR_APP_KEY

# 需要替换为实际的场景标识  
aliyun.captcha.scene=YOUR_SCENE
```

这些参数需要从阿里云验证码控制台获取：
1. 登录阿里云控制台
2. 进入验证码服务
3. 创建应用并获取AppKey和Scene

### 3. 前端集成
前端需要集成阿里云验证码组件，并在验证成功后将以下参数传递给后端：
- `sessionId`: 会话ID
- `sig`: 签名串  
- `token`: 请求唯一标识

## API接口说明

### 1. 登录接口 (mlogin)
**URL**: `/mlogin`  
**方法**: POST  
**参数**:
- `email`: 邮箱地址 (必填)
- `password`: 密码 (必填)
- `sessionId`: 验证码会话ID (可选，如果提供则进行验证码验证)
- `sig`: 验证码签名串 (可选)
- `token`: 验证码请求标识 (可选)

**返回**: User对象，包含登录结果

### 2. 验证码验证接口 (verifyCaptcha)
**URL**: `/verifyCaptcha`  
**方法**: POST  
**参数**:
- `sessionId`: 验证码会话ID (必填)
- `sig`: 验证码签名串 (必填)
- `token`: 验证码请求标识 (必填)

**返回**: 
```json
{
  "success": true/false,
  "message": "验证结果消息"
}
```

## 使用流程

1. 前端显示阿里云验证码组件
2. 用户完成验证码验证
3. 前端获取验证码参数（sessionId, sig, token）
4. 前端调用登录接口，传递邮箱、密码和验证码参数
5. 后端验证验证码，验证通过后进行登录验证
6. 返回登录结果

## 注意事项

1. 验证码参数是可选的，如果不提供则跳过验证码验证
2. 验证码验证失败会直接返回失败，不会进行后续的登录验证
3. 需要确保阿里云AccessKey有验证码服务的权限
4. 建议在生产环境中使用HTTPS协议
5. 验证码有时效性，建议前端在验证成功后立即提交登录请求

## 测试建议

1. 先测试`verifyCaptcha`接口确保验证码验证功能正常
2. 再测试带验证码的登录流程
3. 测试不同的验证码验证失败场景
4. 测试网络异常情况下的处理

## 故障排查

如果遇到问题，请检查：
1. JAR文件是否正确添加到classpath
2. 配置参数是否正确
3. 阿里云AccessKey权限是否足够
4. 网络连接是否正常
5. 查看日志中的详细错误信息
