<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码测试页面</title>
    <style>
        .container {
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        .btn {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .result.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>阿里云验证码测试页面</h1>
        
        <!-- 验证码验证测试 -->
        <div class="section">
            <h3>1. 验证码验证接口测试</h3>
            <p>测试独立的验证码验证接口 <code>/verifyCaptcha</code></p>
            
            <div class="form-group">
                <label for="sessionId">Session ID:</label>
                <input type="text" id="sessionId" placeholder="输入验证码会话ID">
            </div>
            
            <div class="form-group">
                <label for="sig">签名串 (Sig):</label>
                <input type="text" id="sig" placeholder="输入验证码签名串">
            </div>
            
            <div class="form-group">
                <label for="token">Token:</label>
                <input type="text" id="token" placeholder="输入验证码Token">
            </div>
            
            <button class="btn" onclick="testCaptchaVerify()">测试验证码验证</button>
            <button class="btn" onclick="fillMockData()">填入模拟数据</button>
            
            <div id="captchaResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 登录接口测试 -->
        <div class="section">
            <h3>2. 带验证码的登录接口测试</h3>
            <p>测试带验证码参数的登录接口 <code>/mlogin</code></p>
            
            <div class="form-group">
                <label for="loginEmail">邮箱:</label>
                <input type="email" id="loginEmail" placeholder="输入邮箱地址">
            </div>
            
            <div class="form-group">
                <label for="loginPassword">密码:</label>
                <input type="password" id="loginPassword" placeholder="输入密码">
            </div>
            
            <div class="form-group">
                <label for="loginSessionId">Session ID (可选):</label>
                <input type="text" id="loginSessionId" placeholder="输入验证码会话ID">
            </div>
            
            <div class="form-group">
                <label for="loginSig">签名串 (可选):</label>
                <input type="text" id="loginSig" placeholder="输入验证码签名串">
            </div>
            
            <div class="form-group">
                <label for="loginToken">Token (可选):</label>
                <input type="text" id="loginToken" placeholder="输入验证码Token">
            </div>
            
            <button class="btn" onclick="testLogin()">测试登录</button>
            <button class="btn" onclick="testLoginWithoutCaptcha()">测试无验证码登录</button>
            
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 说明信息 -->
        <div class="section">
            <h3>3. 使用说明</h3>
            <ul>
                <li><strong>验证码参数获取：</strong>这些参数通常从前端阿里云验证码组件的success回调中获取</li>
                <li><strong>模拟数据：</strong>点击"填入模拟数据"按钮可以填入测试用的模拟数据</li>
                <li><strong>配置检查：</strong>确保config.properties中的阿里云验证码配置正确</li>
                <li><strong>JAR依赖：</strong>确保已添加阿里云验证码SDK的JAR文件</li>
                <li><strong>网络连接：</strong>确保服务器可以访问阿里云验证码服务</li>
            </ul>
        </div>
    </div>

    <script>
        // 填入模拟数据
        function fillMockData() {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substr(2, 9);
            
            document.getElementById('sessionId').value = 'mock_session_' + timestamp;
            document.getElementById('sig').value = 'mock_sig_' + random;
            document.getElementById('token').value = 'mock_token_' + random;
            
            // 同时填入登录表单
            document.getElementById('loginSessionId').value = 'mock_session_' + timestamp;
            document.getElementById('loginSig').value = 'mock_sig_' + random;
            document.getElementById('loginToken').value = 'mock_token_' + random;
        }
        
        // 测试验证码验证接口
        function testCaptchaVerify() {
            const sessionId = document.getElementById('sessionId').value;
            const sig = document.getElementById('sig').value;
            const token = document.getElementById('token').value;
            
            if (!sessionId || !sig || !token) {
                showResult('captchaResult', '请填写所有验证码参数', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('sessionId', sessionId);
            formData.append('sig', sig);
            formData.append('token', token);
            
            showResult('captchaResult', '正在验证验证码...', 'info');
            
            fetch('/verifyCaptcha', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const message = `验证结果：\n${JSON.stringify(data, null, 2)}`;
                const type = data.success ? 'success' : 'error';
                showResult('captchaResult', message, type);
            })
            .catch(error => {
                console.error('验证码验证请求失败:', error);
                showResult('captchaResult', '请求失败：' + error.message, 'error');
            });
        }
        
        // 测试登录接口
        function testLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            const sessionId = document.getElementById('loginSessionId').value;
            const sig = document.getElementById('loginSig').value;
            const token = document.getElementById('loginToken').value;
            
            if (!email || !password) {
                showResult('loginResult', '请填写邮箱和密码', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('email', email);
            formData.append('password', password);
            
            if (sessionId) formData.append('sessionId', sessionId);
            if (sig) formData.append('sig', sig);
            if (token) formData.append('token', token);
            
            showResult('loginResult', '正在登录...', 'info');
            
            fetch('/mlogin', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const message = `登录结果：\n${JSON.stringify(data, null, 2)}`;
                const type = (data.memberInfoResult && data.memberInfoResult.code === 'SUCCESS') ? 'success' : 'error';
                showResult('loginResult', message, type);
            })
            .catch(error => {
                console.error('登录请求失败:', error);
                showResult('loginResult', '请求失败：' + error.message, 'error');
            });
        }
        
        // 测试无验证码登录
        function testLoginWithoutCaptcha() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            if (!email || !password) {
                showResult('loginResult', '请填写邮箱和密码', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('email', email);
            formData.append('password', password);
            
            showResult('loginResult', '正在登录（无验证码）...', 'info');
            
            fetch('/mlogin', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const message = `登录结果（无验证码）：\n${JSON.stringify(data, null, 2)}`;
                const type = (data.memberInfoResult && data.memberInfoResult.code === 'SUCCESS') ? 'success' : 'error';
                showResult('loginResult', message, type);
            })
            .catch(error => {
                console.error('登录请求失败:', error);
                showResult('loginResult', '请求失败：' + error.message, 'error');
            });
        }
        
        // 显示结果
        function showResult(elementId, message, type) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.textContent = message;
            resultDiv.className = 'result ' + type;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
