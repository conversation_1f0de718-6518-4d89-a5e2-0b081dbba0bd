<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib uri="lhwLink"  prefix="lhw" %>
<!doctype html>
<html lang="en" >
<head>
	<meta charset="UTF-8">
	<title>${city.countryName}${city.cityName}${hotel.hotelNameEn }(${hotel.hotelName })立鼎世酒店集团_lhw.cn</title>
	<meta name="description" content="浏览 ${hotel.hotelNameEn }(${hotel.hotelName })的服务设施、交通信息，酒店图片及最优房价，在线预订访问立鼎世酒店集团官网。${hotel.hotelNameEn }_lhw.cn" />
	<meta name="keywords" content="${city.countryName}${city.cityName}名奢华酒店；${hotel.hotelNameEn }；${hotel.hotelName }"/>
	<link rel='canonical'  href="//www.lhw.cn/hotel/${hotel.hotelLink }/${hotel.hotelcode }"/>
	<link rel="stylesheet" href="/webstatic/css/master.css?20161101">
	<link id="favicon" type="icon shortcut" media="icon" href="/favicon.ico">
	<%@ include file="gtm.jsp" %>
</head>
<body>

<!-- header -->
<div id="header"  style="background: none; height:auto;">
	<jsp:include page="/header.html" />
	<jsp:include page="/searchbar.html" />
</div>
<!-- End header -->

<!-- 	<div class="main" style="margin-top:135px;"> -->
<div class="main">
	<div class="ad" style="height: 700px;">

		<div class="banner" id="adScorll"  style="height: 700px;">
			<ul>
				<c:forEach var="v" items="${hotel.banners }">
					<li>
						<img name ="${v.name }" src="${v.url}" style="height:700px;object-fit:cover;"  alt="${hotel.hotelName }(${hotel.hotelNameEn}) ${v.name }图片  www.lhw.cn"/>
						<div class="img_bg"></div>
					</li>

				</c:forEach>
			</ul>

			<div class="pos-bt">
				<div class="pub-w">
					<div class="pos-ab-photo"></div>
					<div class="pos-ab-di"></div>
					<div class="show-mian">
						<h1>${hotel.hotelName }</h1>
						<h2><span>${hotel.hotelNameEn }</span></h2>
						<h2 class="h2 fixed" style="text-align:left;">
							地址：
							<c:if test="${ hotel.countryCode == 'CN' }">${hotel.address } </c:if>
							<c:if test="${ hotel.countryCode != 'CN' }">${hotel.addressEn },&nbsp;&nbsp;${cityNameEn },&nbsp;&nbsp;${hotel.countryNameEn } </c:if>
							&nbsp;邮编：
							${hotel.postalCode } &nbsp;
							<a href="javascript:;" class="mapLinks">地图</a>
						</h2>
						<button onclick="sendDataLayerHotelDetail('Click_More Pictures','Click_More Pictures')" class="more-photo" url='<c:forEach var="v" items="${AllHotelBanners }">${v.url},</c:forEach>'>
							更多照片
							<img name='<c:forEach var="v" items="${AllHotelBanners }">${v.name },</c:forEach>' alt="${hotel.hotelName }(${hotel.hotelNameEn})  www.lhw.cn"/>
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
	<style type="text/css">
		.detail_to_login{
			width: 1100px;
			height: 139px;
			display: flex;
			align-items: center;
			justify-content: space-evenly;
			margin: 33px auto;
			background-color: #f6f6f6;
			display: none;
		}
		.detail_to_login img{
			width: 232px;
		}
		.detail_to_login_right p:nth-of-type(1){
			color: #666;
			font-size: 16px;
		}
		.detail_to_login_right p:nth-of-type(2){
			color: #666;
			font-size: 14px;
			margin-top: 6px;
			text-align: center;
		}
		.detail_to_login_right p:nth-of-type(2) a{
			color: #4c82ab;
			padding: 0 10px;
		}
	</style>
	<div class="detail_to_login">
		<img src="/webstatic/img/logo-club-dark-minimal.png" alt="">
		<div class="detail_to_login_right">
			<p>免费加入尊享贵宾会享尊贵礼遇</p>
			<p><a href="/register.html">立即注册会籍→</a>或<a id="detail_to_login_btn" onclick="showModalWin()">登录→</a></p>
		</div>
	</div>
	<div class="property-level" id="detail">
		<div class="pub-w">
			<div class="property-tab">
				<ul>
					<li id="viewbyroom">预订客房</li>
					<li id="viewbyinfo">酒店信息</li>
					<li id="viewbymap">地图</li>
				</ul>
				<!--<div style="padding-top:10px;"><c:if test="${hotel.hotelcode =='LW2826'}"><a style="font-size:20px;color:#f05f6a;text-decoration:underline;" href="http://www.lhw.cn/offers/1212sale" target="_blank">12.12 特惠！免费房晚</a></c:if></div>-->
			</div>

			<%-- 			<%@ include file="common/room_list.jsp" %> --%>
			<jsp:include page="common/room_list.jsp" />

		</div>

	</div>


	<div class="perperty-line">
		<div class="pub-w"><div class="tx-rg">需要帮助？欢迎致电400-1324-582</div></div>
	</div>
</div>

<div class="morephoto pop">
	<span class="close-btn"><img src="/webstatic/img/close_btn.png" /></span>
	<div class="ad-fl">
		<div class="big-pic">
			<span class="fl-btn"></span>
			<span class="fr-btn"></span>
			<p><a href="javascript:;"><img src="/webstatic/img/ad01_detail.jpg" alt=""></a></p>
		</div>
		<div class="tx-pic">
			<span></span> <em>外观</em>
		</div>
	</div>
	<div class="ad-fr">
		<ul>
			<li><img src="/webstatic/img/ad01_detail.jpg" alt=""></li>
		</ul>
	</div>
</div>
<!-- End morePhoto -->

<!-- footer -->
<jsp:include page="/footer.html" />
<!-- End footer -->

<div class="score-book-form" id="score_enough">
	<div class="inner-box">
		<div class="header">
			<p>您的积分预订需求我们已经收到，贵宾服务专员会在3-5个工作日与您取得联系，您也可以拨打贵宾服务热线直接进行积分预订。</p>
		</div>
		<div class="phone">
			<a class="phone" href="javascript:void(0);">400-1324-582</a>
		</div>
		<div class="close-icon" onclick="javascript:closeModalWinScoreBook();"><img alt="close" src="/webstatic/img/close_1.png"></div>
	</div>
</div>
<div class="score-book-form" style="width: 370px;height: 190px;" id="score_not_enough">
	<div class="inner-box">
		<div class="header" style="padding-top: 18px;">
			<p>非常抱歉，您的积分不足无法预订。如有疑问请拨打贵宾服务热线</p>
		</div>
		<div class="phone" style="margin-top: 50px;">
			<a class="phone" href="javascript:void(0);">400-1324-582</a>
		</div>
		<div class="close-icon" onclick="javascript:closeModalWinScoreNotEnough();"><img alt="close" src="/webstatic/img/close_1.png"></div>
	</div>
</div>
<div id="bg"></div>
<script src="/webstatic/js/handlebars-v3.0.0.js"></script>
<script src="/webstatic/js/jquery-3.5.1.min.js"></script>
<script src="/webstatic/js/cookie.js"></script>
<script src="/webstatic/js/common.js?v=1.2.2"></script>
<script src="/webstatic/js/httplog.js"></script>

<script id="search-html-rate" type="text/x-handlebars-template">
	{{#compare hotel.conditionCode '=='  2}}
	<div class="error-box">该酒店暂未开业，请<a href="//www.lhw.cn/hotel-search/${lhw:getLink(city.cityNameEn,'-') }/${city.cityCode }">查看其它酒店</a> <br/>
		{{hotel.remark}}
	</div>
	{{/compare}}

	{{#compare hotel.conditionCode '=='  1}}
	{{#if hotel.remark}}
	<div class="error-box">{{hotel.remark}} <br/>请<a href="//www.lhw.cn/hotel-search/${lhw:getLink(city.cityNameEn,'-') }/${city.cityCode }">查看其它酒店</a> </div>
	{{else}}
	<div class="error-box">该酒店装修中，请<a href="//www.lhw.cn/hotel-search/${lhw:getLink(city.cityNameEn,'-') }/${city.cityCode }">查看其它酒店</a> </div>
	{{/if}}
	{{/compare}}

	{{#compare hotel.conditionCode '=='  0}}
	{{#if isAllFull}}
	<div class="error-box">对不起，您搜索日期的客房已全部订完或不接受预订，请选择其他入住日期<br/>
		<span style="color:#435623;font-weight:normal;">{{hotel.remark}}</span>
	</div>
	{{else}}
	<div class="propertylevel-list">
		<input type="hidden" id="hotel" value="{{hotel}}">
		<input type="hidden" id="hotelcode" value="{{hotel.hotelcode}}">
		<input type="hidden" id="hotelNameEn" value="{{hotel.hotelNameEn}}">
		<input type="hidden" id="rooms" value="{{rooms}}">
		<ul >{{#each rates}}
			<li class="fixed">
				<h4><b>{{rateName }}</b> {{#compare rateName '!=' '门市价' }}<span>{{rateDesc }}</span>{{/compare}}</h4>
				{{#each rooms}}
				{{#count 1}}{{/count}}
				<dl class="fixed">
					<dd>
						<p class='more-photo property-p' url='{{#each ../../../roomDetailInfos}}{{#compare ../roomcode '==' roomcode}}{{#each images}}{{url}},{{/each}}{{/compare}}{{/each}}{{#compare roomCoverUrl '==' '//www.lhw.cn/webstatic/img/1.jpg'}}http://www.lhw.cn/webstatic/img/1.jpg,{{/compare}}'>
						<img name="{{roomName}}"  src="{{#picfmt roomCoverUrl '_784_440' '_221_168'}}{{/picfmt}}" alt="${hotel.hotelName }(${hotel.hotelNameEn })  {{roomName }}  www.lhw.cn" /> <i></i></p>
						<div class="property-div">
							<input type="hidden" name="roomcode" value="{{roomcode}}">
							<input type="hidden" name="roomNameEn" value="{{roomNameEn}}">
							<div class="fixed {{vip}}">
								<p>
									<c:choose>
										<c:when test="${hotel.countryCode== 'CN'}">
											<c:if test="${hotel.hotelcode == 'LW1825'}">
												<span>{{roomName }}</span>
												<span><s>{{roomDesc }}</s></span>
											</c:if>
											<c:if test="${hotel.hotelcode != 'LW1825'}">
												<c:choose>
													<c:when test="${hotel.hotelcode == 'LW2847'}">
														{{#compare ratecode '==' 'X9'}}
														<span><b>{{rate_text}}</b></span>
														<span>{{rateDesc}}</span>
														{{else}}
														<span><b>{{rate_text}}</b></span>
														<span>{{rate_description}}</span>
														{{/compare}}
													</c:when>
													<c:otherwise>
														<span><b>{{rate_text}}</b></span>
														{{#compare rate_description '!=' '门市价'}}
														<span>{{rate_description}}</span>
														{{/compare}}
													</c:otherwise>
												</c:choose>
											</c:if>
										</c:when>
										<c:otherwise>
											<span>{{roomName }}</span>
											<span><s>{{roomDesc }}</s></span>
										</c:otherwise>
									</c:choose>

									<span><s>{{roomNameEn}}</s></span>
									<span>
									{{#compare roomRate.freeWifi '=='  'Y'  }}
										<i class="sp-icon6" title="含免费Wi-Fi"></i>
									{{/compare}}
									{{#compare roomRate.breakfastInclusive '=='  'Y'  }}
										<i class="sp-icon7" title="包含早餐"></i>
									{{/compare}}
								</span>
								</p>
								<p class="mar-top mar-top_rate">
								<span>
									{{#compare roomRate.currency '=='  'M_JIFENG'  }}积分<em >{{#numOpForJiFeng roomRate.cnyAverageRate100  '/' 100 }}{{/numOpForJiFeng}}</em>/晚</span>{{/compare}}
									{{#compare roomRate.currency '!='  'M_JIFENG'  }}CNY<em >{{#numOp roomRate.cnyAverageRate100  '/' 100 }}{{/numOp}}</em>/晚</span>{{/compare}}
								</p>
								<p class="mar-top">
									{{#compare roomRate.currency '=='  'M_JIFENG'  }}
										<button onclick="javascript:scoreOrder(this)" >积分预订</button>
									{{/compare}}
									{{#compare roomRate.currency '!='  'M_JIFENG'  }}
										{{#compare vip '==' true}}
											<img class="mar-top_logo" src="/webstatic/leaders-club-hub/logo-club-dark-minimal.png" alt="">
											<button onclick="javascript:order('{{roomRate.roomRateCode}}','{{roomRate.ratecode }}','{{roomRate.rateName }}','{{roomRate.roomcode }}','{{roomName }}',this,'{{roomRate.cnyAverageRate100}}','{{roomRate.roomNameEn}}','vip')">选择会员价预订</button>
										{{/compare}}
										{{#compare vip '==' false}}
										 	<button onclick="javascript:order('{{roomRate.roomRateCode}}','{{roomRate.ratecode }}','{{roomRate.rateName }}','{{roomRate.roomcode }}','{{roomName }}',this,'{{roomRate.cnyAverageRate100}}','{{roomRate.roomNameEn}}')" >立即预订</button>
										{{/compare}}
									{{/compare}}


								</p>
							</div>
						</div>
					</dd>
				</dl>{{/each}}

				{{#countCompare}}
				<div class="more-room hotel-room-more"><u>更多房型</u> <s></s></div>
				{{/countCompare}}

			</li>
			{{/each}}

		</ul>
	</div>
	{{/if}}
	{{/compare}}
</script>

<script id="search-html-room" type="text/x-handlebars-template">
	{{#compare hotel.conditionCode '=='  2}}
	<div class="error-box">该酒店暂未开业，请<a href="//www.lhw.cn/hotel-search/${lhw:getLink1(city.countryNameEn,city.cityNameEn,'-') }/${city.cityCode }">查看其它酒店</a> <br/>
		{{hotel.remark}}
	</div>
	{{/compare}}

	{{#compare hotel.conditionCode '=='  1}}
	{{#if hotel.remark}}
	<div class="error-box">{{hotel.remark}} <br/>请<a href="//www.lhw.cn/hotel-search/${lhw:getLink1(city.countryNameEn, city.cityNameEn,'-') }/${city.cityCode }">查看其它酒店</a> </div>
	{{else}}
	<div class="error-box">该酒店装修中，请<a href="http://www.lhw.cn/hotel-search/${lhw:getLink1(city.countryNameEn,city.cityNameEn,'-') }/${city.cityCode }">查看其它酒店</a> </div>
	{{/if}}
	{{/compare}}

	{{#compare hotel.conditionCode '=='  0}}
	{{#if isAllFull}}
	<div class="error-box">对不起，您搜索日期的客房已全部订完或不接受预订，请选择其他入住日期<br/>
		<span style="color:#435623;font-weight:normal;">{{hotel.remark}}</span>
		 </div>
	{{else}}
	<div class="propertylevel-list">
		<input type="hidden" id="hotel" value="{{hotel}}">
		<input type="hidden" id="hotelcode" value="{{hotel.hotelcode}}">
		<input type="hidden" id="hotelNameEn" value="{{hotel.hotelNameEn}}">
		<input type="hidden" id="rooms" value="{{rooms}}">
		<ul>{{#each rooms}}
			<li class="fixed">
				<c:choose>
					<c:when test="${hotel.countryCode == 'CN'}">
						<c:if test="${hotel.hotelcode == 'LW1825'}">
							<h4><b>{{roomName}}</b>  <span>{{roomDesc }}</span></h4>
						</c:if>
						<c:if test="${hotel.hotelcode != 'LW1825'}">
							<c:choose>
							<c:when test="${hotel.hotelcode == 'LW2847'}">
								<h4><b>{{room_description }}</b>  <span>{{room_text }}</span></h4>
							</c:when>
							<c:otherwise>
								<h4><b>{{room_description }}</b>  <span>{{room_text }}</span></h4>
							</c:otherwise>
							</c:choose>
						</c:if>
					</c:when>
					<c:otherwise>
						<h4><b>{{roomName}}</b>  <span>{{roomDesc }}</span></h4>
					</c:otherwise>
				</c:choose>

				<h6>{{roomNameEn}} </h6>
				<p onclick="sendDataLayerHotelDetail('Click_Room Picture','Click_Room Picture')" class='more-photo property-p' url='{{#each../roomDetailInfos}}{{#compare ../roomcode '==' roomcode}}{{#each images}}{{url}},{{/each}}{{/compare}}{{/each}}{{#compare roomCoverUrl '==' '//www.lhw.cn/webstatic/img/1.jpg'}}http://www.lhw.cn/webstatic/img/1.jpg,{{/compare}}'>
				<img name="{{roomName}}" src="{{#picfmt roomCoverUrl '_784_440' '_221_168'}}{{/picfmt}}" alt="${hotel.hotelName }(${hotel.hotelNameEn }) {{roomName }} www.lhw.cn" />
				<c:if test="{{#picfmt roomCoverUrl '_784_440' '_221_168'}}{{/picfmt}}">
					<i></i>
				</c:if>
				{{#compare roomCoverUrl '!==' ''}}
				<i></i>
				{{/compare}}
				</p>
				<div class="property-div">
					<input type="hidden" name="roomcode" value="{{roomcode}}">
					<input type="hidden" name="roomNameEn" value="{{roomNameEn}}">
					{{#each roomRateList}}
					<div class="fixed {{vip}}">
						<p>
							<c:choose>
								<c:when test="${hotel.countryCode== 'CN'}">
									<c:if test="${hotel.hotelcode == 'LW1825'}">
										<span><b>{{rateName }}</b></span>
										{{#compare rate_description '!=' '门市价' }}
										<span>{{rateDesc }}</span>
										{{/compare}}
									</c:if>
									<c:if test="${hotel.hotelcode != 'LW1825'}">
										<c:choose>
											<c:when test="${hotel.hotelcode == 'LW2847'}">
												{{#compare ratecode '==' 'X9'}}
												<span><b>{{rate_text}}</b></span>
												<span>{{rateDesc}}</span>
												{{else}}
												<span><b>{{rate_text}}</b></span>
												<span>{{rate_description}}</span>
												{{/compare}}
											</c:when>
											<c:otherwise>
												<span><b>{{rate_text}}</b></span>
												{{#compare rate_description '!=' '门市价'}}
												<span>{{rate_description}}</span>
												{{/compare}}
											</c:otherwise>
										</c:choose>
									</c:if>
								</c:when>
								<c:otherwise>
									<span><b>{{rateName }}</b></span>
									{{#compare rateName '!=' '门市价' }}
									<span>{{rateDesc }}</span>
									{{/compare}}
								</c:otherwise>
							</c:choose>

							<span>
							{{#compare freeWifi '=='  'Y'  }}
							<i class="sp-icon6" title="含免费Wi-Fi"></i>
							{{/compare}}
							{{#compare breakfastInclusive '=='  'Y'  }}
							<i class="sp-icon7" title="包含早餐"></i>
							{{/compare}}
						</span>
						</p>
						<p class="mar-top mar-top_rate">
						<span>{{#compare currency '=='  'M_JIFENG'  }}积分<em>{{#numOpForJiFeng cnyAverageRate100  '/' 100 }}{{/numOpForJiFeng}}</em>/晚{{/compare}}
							{{#compare currency '!='  'M_JIFENG'  }}CNY<em>{{#numOp cnyAverageRate100  '/' 100 }}{{/numOp}}</em>/晚{{/compare}}
						</span>

						</p>
						<p class="mar-top">
							{{#compare currency '=='  'M_JIFENG'  }}
								<button onclick="javascript:scoreOrder(this)" >积分预订</button>
							{{/compare}}
							{{#compare currency '!='  'M_JIFENG'  }}
								{{#compare vip '==' true}}
									<img class="mar-top_logo" src="/webstatic/leaders-club-hub/logo-club-dark-minimal.png" alt="">
									<button onclick="javascript:order('{{roomRateCode}}','{{ratecode }}','{{rateName }}','{{roomcode }}','{{../roomName }}',this,'{{cnyAverageRate100}}','{{roomNameEn}}','vip')" >选择会员价预订</button>
								{{/compare}}
								{{#compare vip '==' false}}
									<button onclick="javascript:order('{{roomRateCode}}','{{ratecode }}','{{rateName }}','{{roomcode }}','{{../roomName }}',this,'{{cnyAverageRate100}}','{{roomNameEn}}')" >立即预订</button>
								{{/compare}}
							{{/compare}}
						</p>
					</div>
					{{#count 1}}{{/count}}
					{{/each}}

					{{#countCompare}}
					<p class="more-room"><font>更多价格</font> <s></s></p>
					{{/countCompare}}
				</div>
			</li>{{/each}}
		</ul>
	</div>
	{{/if}}
	{{/compare}}
</script>
<!-- 	<button onclick="javascript:order('{{roomRateCode}}','{{ratecode }}','{{rateName }}','{{roomcode }}','{{../roomName }}',this)" >立即预订</button> -->
<!-- <button onclick="javascript:scoreOrder()" >积分预订</button> -->
<script type="text/javascript" >
	// {{#compare roomRateList.length '>' 1 }}
	// {{/compare}}
	// {{#compare rooms.length '>' 1  }}
	var memberLevel;
	var code1="";
	var isopen_cookie = "lhw.cn_isopen";
	// 	var isopen = $.cookie(isopen_cookie);
	// 	alert(isopen);
	// 	getMemberLevels();
	var templateRate;
	var templateRoom;
	window.pageLoad = true;

	//输入客房数但是未点击重新查询时候将json串修改为一个房间

	function checkField(value){
		$("form input[name='rooms']").val(value);
		if(value > 4){
			value = 4;
		}
		$("form input[name='rooms']").val(value);
	}

	dataLayer.push({
		'event':'productDetail',
		'eventCategory': 'Product_Details',
		'eventAction':'Product Detail View',
		'eventLabel':'',
		'ecommerce':{
			'detail':{
				'products':[{
					'id': '${hotel.hotelcode}',
					'name': "${hotel.hotelNameEn}",
					'category':'',
					'brand':'',
					'variant':'',
					'price':''
				}]
			}
		}
	})

	sessionStorage.removeItem('hotel_order_data');
	//预订
	function order(code,ratecode,ratename,roomcode,roomname,dom,cnyAverageRate100,en,submit_type){
		FH.dispatch("${lhw:getAccountId('lhw.trace.order')}",dom,{hotelName:"${hotel.hotelNameEn}",cityName:"${city.cityNameEn}"});
		var roomNameEn = '';
		roomList.forEach(function(item,index){
			if(item.roomcode === roomcode){
				roomNameEn = item.roomNameEn
			}
		})

		var startTime = $("#form input[name=indate]").val().split('-')[2];
		var endTime = $("#form input[name=outdate]").val().split('-')[2];
		var time =  parseInt(endTime) - parseInt(startTime);
		dataLayer.push({
			'event': 'checkout',
			'eventCategory':'Checkout',
			'eventAction':'Checkout1',
			'eventLabel': 'Go to checkout',
			'dimension7':$("#form input[name=indate]").val() + '::' + $("#form input[name=outdate]").val(),
			'ecommerce':{
				'checkout':{
					'actionField':{'step':1,'option':''},
					'products':[{
						'id': '${hotel.hotelcode}',
						'name': "${hotel.hotelNameEn}",
						'category': '',
						'brand': '',
						'variant': roomcode + '::' + roomNameEn,
						'price': (cnyAverageRate100 / 100) * time,
						'quantity':1
					}]
				}
			}
		})
		setTimeout(function(){
			$("#form input[name=roomRateCode]").val(code);
			$("#form input[name=rateCode]").val(ratecode);
			$("#form input[name=rateName]").val(ratename);
			$("#form input[name=roomCode]").val(roomcode);
			$("#form input[name=roomName]").val(roomname);
			if(submit_type === 'vip' && !$.cookie('lhw.cn_islogin')){
				console.log($("#form").serialize())
				sessionStorage.setItem('hotel_order_data',$("#form").serialize());
				showModalWin(true);
			}else{
				$("#form").submit();
			}
		},200);
	}

	function showModalWinScoreBook() {
		$("#score_enough").show();
		$("#bg").show();
		$("body").addClass("unscroll");
	}
	function closeModalWinScoreBook() {
		$("#score_enough").hide();
		$("#bg").hide();
		$("body").removeClass("unscroll");
	}
	function showModalWinScoreNotEnough() {
		$("#score_not_enough").show();
		$("#bg").show();
		$("body").addClass("unscroll");
	}
	function closeModalWinScoreNotEnough() {
		$("#score_not_enough").hide();
		$("#bg").hide();
		$("body").removeClass("unscroll");
	}

	function datedifference(sDate1, sDate2) {    //sDate1和sDate2是2006-12-18格式
		var dateSpan,
				tempDate,
				iDays;
		sDate1 = Date.parse(sDate1);
		sDate2 = Date.parse(sDate2);
		dateSpan = sDate2 - sDate1;
		dateSpan = Math.abs(dateSpan);
		iDays = Math.floor(dateSpan / (24 * 3600 * 1000));
		return iDays
	};

	function checkScoreEnough(memberScore,score){
		var indate=$("#searchForm input[name=indate]").val()  ;
		var outdate=$("#searchForm input[name=outdate]").val()  ;
		var rooms=$("#searchForm input[name=rooms]").val()  ;
		var days = datedifference(indate,outdate);
		var reslut = memberScore-score*days*rooms;
		if(reslut>=0){
			return true;
		}else{
			return false;
		}
	}

	//积分预订
	function scoreOrder(which){
		var score = $(which).parent().parent().find('p span em').text();
		var memberScore = $.cookie("lhw.cn_member_score");
		$("#bg").addClass("bg");

		if (checkScoreEnough(memberScore,score)) {
			showModalWinScoreBook();
			var uuid = $.cookie("lhw.cn_uuid");
			var hotelcode = $("#hotelcode").val();
			var hotelNameEn = $("#hotelNameEn").val();
			var roomcode = $(which).parent().parent().parent().find("input[name='roomcode']").val();
			console.log(roomcode);
			var roomNameEn = $(which).parent().parent().parent().find("input[name='roomNameEn']").val();
			console.log(roomNameEn);
			var rooms = JSON.stringify($("#rooms").val());
			var indate=$("#searchForm input[name=indate]").val();
			var outdate=$("#searchForm input[name=outdate]").val();
			console.log(indate + "--" +outdate);
			$.ajax({
				url:'/score/reservation',
				type : 'post',
				data :{
					"uuid" : uuid,
					"hotelcode" : hotelcode,
					"roomcode" : roomcode,
					"roomNameEn" : roomNameEn,
					"roomrate" : score,
					"hotelNameEn" : hotelNameEn,
					"indate" : indate,
					"outdate" : outdate
				},
				dataType:'json',
				success:function(data){

				}
			});
		} else {
			showModalWinScoreNotEnough();
		}
	}

	function getMembersJsonStr () {
		var memberStr = '';
		var rooms = parseInt($("#queryForm input[name='rooms']").val());
		var i = 0;
		$(".roomguest-item .room-fr").each(function(){
			if(i<rooms){
				var adult_num = parseInt($(this).find(".item-adult").find("select option:selected").text());
				$("#form input[name=numadult]").val(adult_num);
				var child_num = parseInt($(this).find(".item-children dt").find("select option:selected").text());
				$("#form input[name=numchild]").val(child_num);
				var child_ages = "";

				var j = 0;
				$(this).find("select[name='children_age']").each(function(){
					if(j < child_num){
						child_ages = child_ages + $(this).val()+",";
						j = j +1;
					}
				});
				child_ages = child_ages.substring(0,child_ages.length-1);
				var str ='{' + '"adults":'+adult_num+',"children":'+child_num+',"childAges":"'+child_ages +'"}';
				memberStr = memberStr + str +",";
				i = i+1;
			}
		})
		memberStr = "["+memberStr.substring(0,memberStr.length-1)+"]";
		$("#searchForm input[name='memberStr']").val(memberStr);
		return memberStr
	}

	function getUrl() {
		var promise = $.Deferred();
		var indate=$("#searchForm input[name=indate]").val()  ;
		var outdate=$("#searchForm input[name=outdate]").val()  ;
		var rooms=$("#searchForm input[name=rooms]").val()  ;
		var orderBy= $("#searchForm input[name=orderBy]").val() || "rate";
		var membersJsonStr=$("#searchForm input[name=membersJsonStr]").val();
		membersJsonStr = getMembersJsonStr(membersJsonStr);
		$("#form input[name=membersJsonStr]").val(membersJsonStr);
		$("#form input[name=indatromme]").val(indate)  ;
		$("#form input[name=outdate]").val(outdate)  ;
		$("#form input[name=rooms]").val(rooms)  ;


		var url="/hotel/${hotelName}/${hotelCode}/${sabrecode}/ajax?numadult=${numadult }&numchild=${ numchild}&indate="+indate+"&outdate="+outdate+"&rooms="+rooms+"&orderBy=" + orderBy +"&membersJsonStr=" +membersJsonStr;

		//addLog("wwwlhwcn","www","info","获取房价查询url: "+url);
		//获取indate,outdate,rooms,orderBy
		promise.resolve(url);
		return promise.promise();
	}

	function getData(url){
		return $.ajax(url);
	}

	function reSearch(){
		getMemberLevels();
		$.when(
				getUrl())
				.then(function(url) {
					$("#search-result").html("<div class='error-box'>正在加载中，请稍候……</div>");
					return getData(url);
				}).then(function(data) {
			//addLog("wwwlhwcn","www","info","获取房价查询结果: "+data);
			console.log(data);
			data["code1"] = code1;
			window.roomList = data.rooms || []
			var html;
			if ( $("#searchForm input[name=orderBy]").val()==='rate'){
				var rates = data["rates"];
				for(var i=0; i<rates.length; i++) {
					var rate = rates[i];
					if(typeof rate["rateDesc"] != "undefined" && rate["rateDesc"] != null && rate["rateDesc"] != "") {
						rate["rateDesc"] = rate["rateDesc"].replace(/<[^>]+>/g,"");
						rate["rateName"] = rate["rateName"].replace("\n"," ");
					}
					var rooms = rate["rooms"];
					var allRoom = [];
					rooms.map(item=>{
						if(item.roomRateList && item.roomRateList.length > 0){
							item.roomRateList.map(itemB=>{
								if(itemB.ratecode === rate.ratecode){
									allRoom.push({
										...item,
										cny_rate100:itemB.cny_rate100,
										ratecode:itemB.ratecode,
										roomRate:itemB
									})
								}
							})
						}
					})
					rate["rooms"] = sortRate(allRoom,'rate',code1);
				}

				var ratesAll = [];
				var _rates = [];
				var _ratesNLTFG = [];
				if(rates){
					rates.map(item=>{
						if(item.rooms.length!==0){
							if(item.ratecode === "NLTF" || item.ratecode === "NLTG"){
								_ratesNLTFG.push(item);
							}else{
								_rates.push(item);
							}
						}
					})
				}
				if(_ratesNLTFG.length>0){ ratesAll = ratesAll.concat(_ratesNLTFG); }
				if(_rates.length>0){ ratesAll = ratesAll.concat(_rates); }
				rates = ratesAll;
				data.rates = rates;
				console.log(rates,'======rates')
				if (data.rates.length == 0) {
					data["isAllFull"] = true;
				}
				html = templateRate(data);
			}else{
				var rooms = data["rooms"];
				for(var i=0; i<rooms.length; i++) {
					var room = rooms[i];
					if(typeof room["roomDesc"] != "undefined" && room["roomDesc"] != null && room["roomDesc"] != "") {
						room["roomDesc"] = room["roomDesc"].replace(/<[^>]+>/g,"");
					}
					var roomRateLists = room["roomRateList"];
					room["roomRateList"] = sortRate(roomRateLists,'room',code1);
				}
				var _rooms = [];
				if(rooms){
					rooms.map(item=>{
						item.roomRateList.length!==0 ?_rooms.push(item): null;
					})	
				}
				rooms = _rooms;
				data.rooms = rooms;
				console.log(rooms,'======rooms')
				if (data.rooms.length == 0) {
					data["isAllFull"] = true;
				}
				html = templateRoom(data);
			}
			$('#search-result').html(html);
			$(".propertylevel-list li").find("dl:eq(0)").show();
			$(".propertylevel-list li div").find("div:eq(0)").show();
			// map
			var href = window.location.href;
			if(href.indexOf("#map")!=-1){
				$("#viewbymap").click();
			}
		}, function(err) {
			///addLog("wwwlhwcn","www","error","房价查询失败,"+err);
		});
	}


	function sortRate(list,type,code){
		var roomRateVIP = [],roomRate = [],roomNLTFG = [],allList = [];
		list && list.map((roomRateList,index)=>{
			if(type === 'room'){
				if(typeof roomRateList["rateDesc"] != "undefined" && roomRateList["rateDesc"] != null && roomRateList["rateDesc"] != "") {
					roomRateList["rateDesc"] = roomRateList["rateDesc"].replace(/<[^>]+>/g,"");
					roomRateList["rateName"] = roomRateList["rateName"].replace("\n"," ");
				}
			}else{
				if(typeof roomRateList.roomRate["roomDesc"] != "undefined" && roomRateList.roomRate["roomDesc"] != null && roomRateList.roomRate["roomDesc"] != "") {
					roomRateList.roomRate["roomDesc"] = roomRateList.roomRate["roomDesc"].replace(/<[^>]+>/g,"");
				}
			}
			if(!$.cookie('lhw.cn_islogin')){
				if(roomRateList["ratecode"] == "X9" ||
					roomRateList["ratecode"] == "X19" ||
					roomRateList["ratecode"] == "XX" ||
					roomRateList["ratecode"] == "XLAR"
				){
					roomRateList.vip = true;
					roomRateVIP.push(roomRateList);
				}else if(roomRateList["ratecode"] == "NLTG" || roomRateList["ratecode"] == "NLTF"){
					if(code === roomRateList["ratecode"]){
						roomRateList.vip = false;
						roomNLTFG.push(roomRateList);
					}
				}else{
					roomRateList.vip = false;
					roomRate.push(roomRateList);
				}
			}else{
				if(roomRateList["ratecode"] == "NLTG" || roomRateList["ratecode"] == "NLTF"){
					if(code === roomRateList["ratecode"]){
						roomRateList.vip = false;
						roomNLTFG.push(roomRateList);
					}
				}else{
					roomRateList.vip = false;
					roomRate.push(roomRateList);
				}
			}
		})

		if(!$.cookie('lhw.cn_islogin')){
			//vip价格排序
			if(roomRateVIP.length>0){
				roomRateVIP.sort(function(a,b){
					return a.cny_rate100 - b.cny_rate100;
				})
				allList = allList.concat(roomRateVIP);
			}
			if(roomRate.length>0){
				allList = allList.concat(roomRate);
			}
		}else{
			if(roomNLTFG.length>0){
				allList = allList.concat(roomNLTFG);
			}
			if(roomRate.length>0){
				allList = allList.concat(roomRate);
			}
		}
		return allList
	}

	function getMemberLevels() {
// 		Club、Sterling & Aurelian
		var memberLevel = getCookie("lhw.cn_member_level");
		console.log("memberLevel:" + memberLevel);
// 		alert(memberLevel);
		var isopen = $.cookie(isopen_cookie);
		var memberStats = $.cookie(renew_cookie);
		if(memberStats == "Active"){
			if (memberLevel == 'Club' || memberLevel == 'Sterling') {
				code1="";
				if(isopen=="true"){
					code1="NLTF";
				}else{
					code1="X";
				}


			} else if (memberLevel == 'Aurelian') {
				code1="";
				if(isopen=="true"){
					code1="NLTG";
				}else{
					code1="X";
				}
			}
		}
	}
	//数据回显
	function dataReShow () {
		console.log('dataReShowdataReShowdataReShow');
		var adults_total = 0,
				children_total = 0,
				jsonData = $("input[name ='membersJsonStr']").val();
		jsonData = eval(jsonData);

		if(!!jsonData){
			if(!!jsonData.length) {
				$(".roomguest-list .roomguest-item").hide();
				jsonData.forEach(function(ele, index){
					console.log(ele, index);
					$(".roomguest-list .roomguest-item:eq("+index+")").show();
					$(".roomguest-list .roomguest-item:eq("+index+")").find(".item-adult").find("select option").eq(ele.adults-1).attr("selected","selected");
					$(".roomguest-list .roomguest-item:eq("+index+")").find(".item-children dt").find("select option").eq(ele. children).attr("selected","selected");
					adults_total = adults_total + ele.adults;
					children_total = children_total + ele.children;
					if(!!ele.childAges){
						var childage = ele.childAges.split(",")
						childage.forEach(function(age, indexAge){
							$(".roomguest-list .roomguest-item:eq("+index+")").find("dl dd:eq(1)").show();
							$(".roomguest-list .roomguest-item:eq("+index+")").find("dl dd:eq("+indexAge+")").show();
							$(".roomguest-list .roomguest-item:eq("+index+")").find("dl dd:eq("+indexAge+")").find("select option").eq(age-1).attr("selected","selected");
						})
					}
				})
			}
		}
	}
	$(document).ready(function(){
		templateRate =  templateRate ||  Handlebars.compile($("#search-html-rate").html());
		templateRoom = templateRoom ||  Handlebars.compile($("#search-html-room").html());
		var indate = $("#indate").val(),
				outdate = $("#outdate").val(),
				indateYear = indate.substring(0,4),
				indateMon = indate.substring(4,6),
				indateDay = indate.substring(6,999),
				outdateYear = outdate.substring(0,4),
				outdateMon = outdate.substring(4,6),
				outdateDay = outdate.substring(6,999),
				$attrNum = 0,
				roomNum = $('.select-room span.ipt-2').text(),
				$adultPerson = $('#searchForm .adult-search'),
				$children = $('#searchForm .child-search'),
				$adultDiv = $('#searchForm .roomguest-select');
		//数据回显
		dataReShow();
		reSearch();
		// 客房数
		$("input[name='rooms']").blur(function(){
			var reg = /^[0-9]*$/,
					value = $(this).val();
			if(reg.test(value) == false){
				alert("请输入小于等于4的数字");
				$(this).val(1);
			}else{
				if(value > 4){
					$(this).val(4);
				}
			}

		});

		// 排序方式
		var href = location.href,
				zhi = href.substring(href.indexOf("orderBy")+8,href.indexOf("orderBy")+12);
		if(zhi == "room"){
			$("#typeSelect > span").text("按房型类别");
		}else if( zhi == "rate"){
			$("#typeSelect > span").text("按价格类别");
		}else{
			var orderBy = $("input[name='orderBy']").val();
			if(orderBy == "room"){
				$("#typeSelect > span").text("按房型类别");
			}else if(orderBy == "rate"){
				$("#typeSelect > span").text("按价格类别");
			}
		}

		$(".type-select p").on("click",function(){
			var v = $(this).attr("v");
			$("input[name='orderBy']").val(v);
			//点击排序类型的时候进行重新查询
			reSearch();
			var type = $("input[name='orderBy']").val() === 'room' ? 'Room Type' : 'Price';
			sendDataLayerHotelDetail('Click_CenterSearch','Sort By::' + type)

		});

		// 查看详情
		$("body").on("click",".detailLinks",function(){
			$(".property-tab li").removeClass("active").eq(1).addClass("active");
			$(".property-content .tab-content").hide().eq(1).show();
		});

		$(".pos-ab-photo,.pos-ab-di").height($(".show-mian").innerHeight());

		getMemberLevels();


		$('.select-room').on('click', 'span:eq(1)', function(){
			//$(this).parent().find('.roomguest-select').show();
		})
		$('#searchForm').on('mouseleave', '.roomguest-select', function(){
			$(this).hide();
		})
		$('#searchForm .adult-search, #searchForm .child-search').click(function(){
			$('.roomguest-box').show();
		})
		// 选择客房数
		$adultDiv.find("p").click(function(){
			var num = $(this).text(),
					text = 0,
					text2 = 0;

			$(".roomguest-box .roomguest-item").hide();
			$(".roomguest-box .roomguest-item:lt("+num+")").show();
			$(this).parent().siblings('span.ipt-2').text(num);
			$("input[name='rooms']").val(num);

			if(num > 0){
				//儿童年龄默认为0 $(".roomguest-list .roomguest-item:eq("+(num-1)+")").find("dt").find("select option").eq(0).attr("selected","selected");
				// $(".roomguest-list .roomguest-item:eq("+(num-1)+")").find("dd").hide();

				$(".roomguest-list .roomguest-item:lt("+num+")").each(function(){
					text += parseInt($(this).find(".item-adult").find("select option:selected").text());
					text2 += parseInt($(this).find(".item-children dt").find("select option:selected").text());
				});

				$adultPerson.text(text);
				$children.text(text2);
			} else {
				text = $(".roomguest-list .roomguest-item:eq(0)").find(".item-adult").find("select option:selected").text();
				text2 = $(".rooms-list .roomguests-list:eq(0)").find(".item-children dt").find("select option:selected").text();
				$adultPerson.text(text);
				$children.text(text2);
				$("input[name='numadult']").val(text);
				/* 				$adultPerson.find(".select-hide").addClass('select-main');
                                $adultPerson.removeAttr("id");		 */
			}
		})

		//选择成人数+儿童数
		$(".roomguest-box .roomguest-item").each(function(index, ele){
			var $optionAdult = $(this).find(".item-adult").find("select"),
					$optionChild = $(this).find(".item-children dt").find("select");

			$optionAdult.change(function(){
				var	roomNum = $adultDiv.parent().find("span.ipt-2").text(),
						text = 0;
				$(".roomguest-list .roomguest-item:lt("+roomNum+")").each(function(){
					text += parseInt($(this).find(".item-adult").find("select option:selected").text());
				});
				$adultPerson.text(text);
			})
			$optionChild.change(function(){
				var num = $(this).find("option:selected").text();
				var	roomNum = $adultDiv.parent().find("span.ipt-2").text(),
						text = 0;
				console.log(num, roomNum);
				$(".roomguest-list .roomguest-item:lt("+roomNum+")").each(function(){
					text += parseInt($(this).find(".item-children dt").find("select option:selected").text());
				});
				$children.text(text);
				$(this).parents(".item-children").find("dd").hide();
				$(this).parents(".item-children").find("dd:lt("+(parseInt(num))+")").show();
				$("input[name='numchild']").val(text);
			})
		})

		$(".roomguest-box .closeBtn").click(function(){
			$('.roomguest-box').hide();
		})
		console.log(roomNum);

		//$adultDiv.find("p:eq("+(roomNum-1)+")").trigger("click");

	});
	setTimeout(function(){
		FH.dispatch("${lhw:getAccountId('lhw.trace.hotelDetail')}",$('body')[0],{hotelName:"${hotel.hotelNameEn}",cityName:"${city.cityNameEn}"});
	},200);

</script>
<script>
	var dataLayer = window.dataLayer || [];
	function sendDataLayerHotelDetail(eventAction,eventLabel){
		dataLayer.push({
			"event":"uaevent",
			"eventCategory":'Product_Details',
			"eventAction":eventAction,
			"eventLabel":eventLabel,
		});

		if(eventAction === 'Click_More Pictures'){
			sendDataLayerHotelDetail('View_More Pictures','View_Picture Popups')
		}
	}
	$('.close-btn').on('click',function(){
		sendDataLayerHotelDetail('Close_More Pictures','Close_Picture Popups')
	})
	$('#viewbyroom').on('click',function(){
		sendDataLayerHotelDetail('Click_CenterNav','Room Reservations')
	})
	$('#viewbyinfo').on('click',function(){
		sendDataLayerHotelDetail('Click_CenterNav','Hotel Info')
	})
	$('#viewbymap').on('click',function(){
		sendDataLayerHotelDetail('Click_CenterNav','Map')
	})

	var show_login_msg = sessionStorage.getItem('show_login_msg');
	if(show_login_msg !== '1' && show_login_msg !== '2'){
		sessionStorage.setItem('show_login_msg',1);
	}
	if(show_login_msg === '2' && !$.cookie('lhw.cn_islogin')){
		$('.detail_to_login').css('display','flex')
		sessionStorage.setItem('show_login_msg',1);
	}


	// $('.more-photo property-p').on('click',function(){
	// 	sendDataLayerHotelDetail('Click_Room Picture','Click_Room Picture')
	// })
</script>
</body>

</html>
<!-- /pages/hotel_detail_rate.jsp -->