<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阿里云验证码2.0测试页面</title>
    <style>
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .btn {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .result.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>阿里云验证码2.0测试页面</h1>
        
        <!-- 验证码2.0验证测试 -->
        <div class="section">
            <h3>1. 验证码2.0验证接口测试</h3>
            <p>测试独立的验证码2.0验证接口 <code>/verifyCaptcha2</code></p>
            
            <div class="form-group">
                <label for="captchaVerifyParam">CaptchaVerifyParam:</label>
                <textarea id="captchaVerifyParam" placeholder="输入验证码验证参数（JSON格式）"></textarea>
                <small>示例格式：{"sceneId":"xxxxxx","certifyId":"xxxxxx","deviceToken":"xxxxxxx==","data":"xxxxxx==","..."}</small>
            </div>
            
            <div class="form-group">
                <label for="sceneId">Scene ID (可选):</label>
                <input type="text" id="sceneId" placeholder="输入场景ID">
            </div>
            
            <button class="btn" onclick="testCaptcha2Verify()">测试验证码2.0验证</button>
            <button class="btn" onclick="fillMockCaptcha2Data()">填入模拟数据</button>
            
            <div id="captcha2Result" class="result" style="display: none;"></div>
        </div>
        
        <!-- 登录接口测试 -->
        <div class="section">
            <h3>2. 带验证码2.0的登录接口测试</h3>
            <p>测试带验证码2.0参数的登录接口 <code>/mlogin</code></p>
            
            <div class="form-group">
                <label for="loginEmail">邮箱:</label>
                <input type="email" id="loginEmail" placeholder="输入邮箱地址">
            </div>
            
            <div class="form-group">
                <label for="loginPassword">密码:</label>
                <input type="password" id="loginPassword" placeholder="输入密码">
            </div>
            
            <div class="form-group">
                <label for="loginCaptchaVerifyParam">CaptchaVerifyParam (可选):</label>
                <textarea id="loginCaptchaVerifyParam" placeholder="输入验证码验证参数"></textarea>
            </div>
            
            <div class="form-group">
                <label for="loginSceneId">Scene ID (可选):</label>
                <input type="text" id="loginSceneId" placeholder="输入场景ID">
            </div>
            
            <button class="btn" onclick="testLogin2()">测试登录</button>
            <button class="btn" onclick="testLoginWithoutCaptcha2()">测试无验证码登录</button>
            
            <div id="login2Result" class="result" style="display: none;"></div>
        </div>
        
        <!-- 说明信息 -->
        <div class="section">
            <h3>3. 验证码2.0使用说明</h3>
            <h4>主要变化：</h4>
            <ul>
                <li><strong>接口变更：</strong>使用 <code>VerifyIntelligentCaptcha</code> 接口</li>
                <li><strong>参数简化：</strong>只需要 <code>captchaVerifyParam</code> 和可选的 <code>sceneId</code></li>
                <li><strong>参数格式：</strong>captchaVerifyParam 是前端验证码组件回调提供的完整参数</li>
                <li><strong>服务地址：</strong>使用新的 endpoint 地址</li>
            </ul>
            
            <h4>CaptchaVerifyParam 格式示例：</h4>
            <div class="code-block">
V2架构示例：
{"sceneId":"xxxxxx","certifyId":"xxxxxx","deviceToken":"xxxxxxx==","data":"xxxxxx==","..."}

V3架构示例：
eyJjZXxxxxxxxxxxxxxxnVlfQ==
            </div>
            
            <h4>返回码说明：</h4>
            <ul>
                <li><strong>T001：</strong>服务端校验通过</li>
                <li><strong>T005：</strong>控制台开启测试模式，且配置了验证通过</li>
                <li><strong>F001：</strong>疑似攻击请求，风险策略不通过</li>
                <li><strong>F002：</strong>CaptchaVerifyParam参数为空</li>
                <li><strong>F003：</strong>CaptchaVerifyParam格式不合法</li>
                <li><strong>F004：</strong>控制台开启测试模式，且配置了验证不通过</li>
                <li><strong>更多错误码：</strong>请参考阿里云验证码2.0官方文档</li>
            </ul>
            
            <h4>配置要求：</h4>
            <ul>
                <li>确保已添加验证码2.0的SDK依赖</li>
                <li>配置正确的AccessKey和AccessKeySecret</li>
                <li>设置正确的endpoint和regionId</li>
                <li>在阿里云控制台创建验证码场景</li>
            </ul>
        </div>
    </div>

    <script>
        // 填入模拟验证码2.0数据
        function fillMockCaptcha2Data() {
            const mockData = {
                sceneId: "mock_scene_" + Date.now(),
                certifyId: "mock_certify_" + Math.random().toString(36).substr(2, 9),
                deviceToken: btoa("mock_device_token_" + Date.now()),
                data: btoa("mock_data_" + Math.random().toString(36).substr(2, 9)),
                timestamp: Date.now()
            };
            
            const mockJson = JSON.stringify(mockData);
            document.getElementById('captchaVerifyParam').value = mockJson;
            document.getElementById('sceneId').value = mockData.sceneId;
            
            // 同时填入登录表单
            document.getElementById('loginCaptchaVerifyParam').value = mockJson;
            document.getElementById('loginSceneId').value = mockData.sceneId;
        }
        
        // 测试验证码2.0验证接口
        function testCaptcha2Verify() {
            const captchaVerifyParam = document.getElementById('captchaVerifyParam').value;
            const sceneId = document.getElementById('sceneId').value;
            
            if (!captchaVerifyParam) {
                showResult('captcha2Result', '请填写CaptchaVerifyParam参数', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('captchaVerifyParam', captchaVerifyParam);
            if (sceneId) formData.append('sceneId', sceneId);
            
            showResult('captcha2Result', '正在验证验证码2.0...', 'info');
            
            fetch('/verifyCaptcha2', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const message = `验证结果：\n${JSON.stringify(data, null, 2)}`;
                const type = data.success ? 'success' : 'error';
                showResult('captcha2Result', message, type);
            })
            .catch(error => {
                console.error('验证码2.0验证请求失败:', error);
                showResult('captcha2Result', '请求失败：' + error.message, 'error');
            });
        }
        
        // 测试登录接口
        function testLogin2() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            const captchaVerifyParam = document.getElementById('loginCaptchaVerifyParam').value;
            const sceneId = document.getElementById('loginSceneId').value;
            
            if (!email || !password) {
                showResult('login2Result', '请填写邮箱和密码', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('email', email);
            formData.append('password', password);
            
            if (captchaVerifyParam) formData.append('captchaVerifyParam', captchaVerifyParam);
            if (sceneId) formData.append('sceneId', sceneId);
            
            showResult('login2Result', '正在登录...', 'info');
            
            fetch('/mlogin', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const message = `登录结果：\n${JSON.stringify(data, null, 2)}`;
                const type = (data.memberInfoResult && data.memberInfoResult.code === 'SUCCESS') ? 'success' : 'error';
                showResult('login2Result', message, type);
            })
            .catch(error => {
                console.error('登录请求失败:', error);
                showResult('login2Result', '请求失败：' + error.message, 'error');
            });
        }
        
        // 测试无验证码登录
        function testLoginWithoutCaptcha2() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            if (!email || !password) {
                showResult('login2Result', '请填写邮箱和密码', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('email', email);
            formData.append('password', password);
            
            showResult('login2Result', '正在登录（无验证码）...', 'info');
            
            fetch('/mlogin', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const message = `登录结果（无验证码）：\n${JSON.stringify(data, null, 2)}`;
                const type = (data.memberInfoResult && data.memberInfoResult.code === 'SUCCESS') ? 'success' : 'error';
                showResult('login2Result', message, type);
            })
            .catch(error => {
                console.error('登录请求失败:', error);
                showResult('login2Result', '请求失败：' + error.message, 'error');
            });
        }
        
        // 显示结果
        function showResult(elementId, message, type) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.textContent = message;
            resultDiv.className = 'result ' + type;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
