<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阿里云验证码集成示例</title>
    <style>
        .login-form {
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        .captcha-container {
            margin: 15px 0;
            min-height: 40px;
        }
        .btn {
            width: 100%;
            padding: 10px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .message {
            margin-top: 10px;
            padding: 10px;
            border-radius: 3px;
        }
        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="login-form">
        <h2>登录示例 - 阿里云验证码</h2>
        <form id="loginForm">
            <div class="form-group">
                <label for="email">邮箱:</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <!-- 阿里云验证码容器 -->
            <div class="captcha-container">
                <div id="captcha-element"></div>
            </div>
            
            <button type="submit" class="btn" id="loginBtn">登录</button>
        </form>
        
        <div id="message"></div>
    </div>

    <!-- 阿里云验证码SDK -->
    <!-- 注意：需要替换为实际的阿里云验证码SDK地址 -->
    <script src="https://g.alicdn.com/sd/ncpc/nc.js"></script>
    
    <script>
        // 验证码相关变量
        let captchaData = {
            sessionId: '',
            sig: '',
            token: ''
        };
        
        // 初始化阿里云验证码
        function initCaptcha() {
            // 注意：以下配置需要根据实际的阿里云验证码配置进行调整
            const captchaConfig = {
                // 应用标识，需要从阿里云控制台获取
                appkey: 'YOUR_APP_KEY',
                // 场景标识，需要从阿里云控制台获取  
                scene: 'YOUR_SCENE',
                // 验证码容器ID
                elementId: 'captcha-element',
                // 验证码类型：1-滑动验证，2-点击验证，3-智能验证
                type: 1,
                // 验证码语言
                language: 'cn',
                // 验证成功回调
                success: function(data) {
                    console.log('验证码验证成功:', data);
                    captchaData.sessionId = data.sessionId;
                    captchaData.sig = data.sig;
                    captchaData.token = data.token;
                    
                    // 启用登录按钮
                    document.getElementById('loginBtn').disabled = false;
                    showMessage('验证码验证通过，可以登录了', 'success');
                },
                // 验证失败回调
                fail: function(data) {
                    console.log('验证码验证失败:', data);
                    captchaData = { sessionId: '', sig: '', token: '' };
                    
                    // 禁用登录按钮
                    document.getElementById('loginBtn').disabled = true;
                    showMessage('验证码验证失败，请重新验证', 'error');
                },
                // 验证码加载完成回调
                ready: function() {
                    console.log('验证码加载完成');
                }
            };
            
            // 初始化验证码（这里使用伪代码，实际需要根据阿里云SDK文档调整）
            // window.AWSC.use('nc', function(state, module) {
            //     window.nc = module.init(captchaConfig);
            // });
            
            // 临时模拟验证码初始化（实际使用时请删除）
            simulateCaptcha();
        }
        
        // 模拟验证码功能（仅用于演示，实际使用时请删除）
        function simulateCaptcha() {
            const captchaElement = document.getElementById('captcha-element');
            captchaElement.innerHTML = `
                <div style="border: 1px solid #ddd; padding: 10px; text-align: center; background: #f9f9f9;">
                    <p>模拟验证码区域</p>
                    <button type="button" onclick="simulateSuccess()" style="margin: 5px;">模拟验证成功</button>
                    <button type="button" onclick="simulateFail()" style="margin: 5px;">模拟验证失败</button>
                </div>
            `;
            
            // 初始状态禁用登录按钮
            document.getElementById('loginBtn').disabled = true;
        }
        
        // 模拟验证成功
        function simulateSuccess() {
            captchaData = {
                sessionId: 'mock_session_' + Date.now(),
                sig: 'mock_sig_' + Math.random().toString(36).substr(2, 9),
                token: 'mock_token_' + Math.random().toString(36).substr(2, 9)
            };
            document.getElementById('loginBtn').disabled = false;
            showMessage('模拟验证码验证通过', 'success');
        }
        
        // 模拟验证失败
        function simulateFail() {
            captchaData = { sessionId: '', sig: '', token: '' };
            document.getElementById('loginBtn').disabled = true;
            showMessage('模拟验证码验证失败', 'error');
        }
        
        // 显示消息
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = 'message ' + type;
            
            // 3秒后清除消息
            setTimeout(() => {
                messageDiv.textContent = '';
                messageDiv.className = '';
            }, 3000);
        }
        
        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                showMessage('请填写邮箱和密码', 'error');
                return;
            }
            
            // 构建登录请求数据
            const loginData = new FormData();
            loginData.append('email', email);
            loginData.append('password', password);
            
            // 如果有验证码数据，则添加到请求中
            if (captchaData.sessionId) {
                loginData.append('sessionId', captchaData.sessionId);
                loginData.append('sig', captchaData.sig);
                loginData.append('token', captchaData.token);
            }
            
            // 发送登录请求
            fetch('/mlogin', {
                method: 'POST',
                body: loginData
            })
            .then(response => response.json())
            .then(data => {
                console.log('登录响应:', data);
                
                if (data.memberInfoResult && data.memberInfoResult.code === 'SUCCESS') {
                    showMessage('登录成功！', 'success');
                    // 这里可以跳转到其他页面
                    // window.location.href = '/dashboard';
                } else {
                    const message = data.memberInfoResult ? data.memberInfoResult.message : '登录失败';
                    showMessage(message || '登录失败，请重试', 'error');
                    
                    // 如果是验证码相关错误，重置验证码
                    if (message && message.includes('验证码')) {
                        captchaData = { sessionId: '', sig: '', token: '' };
                        document.getElementById('loginBtn').disabled = true;
                        // 重新初始化验证码
                        initCaptcha();
                    }
                }
            })
            .catch(error => {
                console.error('登录请求失败:', error);
                showMessage('网络错误，请重试', 'error');
            });
        });
        
        // 页面加载完成后初始化验证码
        document.addEventListener('DOMContentLoaded', function() {
            initCaptcha();
        });
    </script>
</body>
</html>
