body {
  background: #f4f4f4;
}
.member-login {
  font-family: "Open Sans";
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.1em;
  line-height: normal;
  font-size: 12px;
}
@media (min-width: 768px) {
  .member-login {
    font-size: 14px;
  }
}
.member-login a {
  text-decoration: underline;
}
.stripe {
  display: flex;
  padding: 8px;
  background: #192430;
}
.stripe .dropdown {
  z-index: 2;
}
@media (max-width: 767.98px) {
  .stripe .dropdown {
    margin-right: 30px;
    white-space: nowrap;
  }

}
.stripe .dropdown-toggle {
  padding: 5px;
  color: #fff;
}
.stripe .dropdown-toggle:after {
  margin-left: 8px;
  margin-top: 3px;
}
.stripe .dropdown-toggle .svg-icon {
  margin-bottom: -4px;
  width: 18px;
  height: 15px;
}
.stripe .member-login {
  margin-left: auto;
  color: #fff;
  max-width: 60%;
  line-height: 24px;
}
@media (min-width: 768px) {
  .stripe .member-login {
    max-width: 80%;
  }
}
.stripe .member-login a {
  color: #fff;
}
.stripe .member-login a:hover {
  color: #e8e8e8;
}
@media (max-width: 767.98px) {
  .page-container {
    padding: 0;
  }
}
@media (min-width: 1200px) {
  .page-container {
    max-width: 1396px;
  }
}
.leaders-club-hub {
  padding-bottom: 50px;
  background: #fff;
}
@media (max-width: 991.98px) {
  .leaders-club-hub {
    overflow: hidden;
  }
}
.leaders-club-hub .hero {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 250px;
  background-color: #192430;
}
@media (min-width: 768px) {
  .leaders-club-hub .hero {
    height: 300px;
  }
}
.leaders-club-hub .hero .logos {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px;
  width: 100%;
  background: #fff;
  text-align: center;
}
@media (min-width: 768px) {
  .leaders-club-hub .hero .logos {
    padding: 20px;
    background: rgba(255, 255, 255, 0.85);
  }
}
@media (min-width: 992px) {
  .leaders-club-hub .hero .logos {
    padding: 30px;
  }
}
@media (min-width: 768px) {
  .leaders-club-hub .hero .logos.solid {
    background: linear-gradient(
      to left,
      rgba(255, 255, 255, 0.9),
      #fff 15%,
      #fff 85%,
      rgba(255, 255, 255, 0.9) 100%
    );
  }
}
@media (min-width: 992px) {
  .leaders-club-hub .hero .logos.solid {
    background: linear-gradient(
      to left,
      rgba(255, 255, 255, 0.9),
      #fff 30%,
      #fff 70%,
      rgba(255, 255, 255, 0.9) 100%
    );
  }
}
.leaders-club-hub .hero .logos .logo {
  max-height: 50px;
  max-width: 30%;
}
@media (min-width: 768px) {
  .leaders-club-hub .hero .logos .logo {
    max-height: 68px;
    max-width: 160px;
  }
  .leaders-club-hub .hero .logos .logo-lc{
    width: 160px;
  }
}
.leaders-club-hub .hero .logos .separator {
  display: inline-block;
  margin: 0 20px;
  width: 1px;
  height: 100%;
  background: #e8e8e8;
}
@media (min-width: 576px) {
  .leaders-club-hub .hero .logos .separator {
    margin: 0 40px;
  }
}
@media (min-width: 768px) {
  .leaders-club-hub .hero .logos .separator {
    margin: 0 50px;
  }
}
.leaders-club-hub .hero .hero-image {
  position: absolute;
  z-index: 1;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-size: cover;
  background-position: center center;
  opacity: 0.7;
}
.leaders-club-hub .hero .copy {
  position: relative;
  z-index: 2;
  margin: auto;
  color: #fff;
  text-align: center;
  text-shadow: 0px 0px 70px #192430;
}
.leaders-club-hub .hero .copy .subtitle {
  margin-bottom: 5px;
  position: relative;
  font-weight: 600;
  letter-spacing: 0.15em;
}
.leaders-club-hub .hero .copy .title {
  margin: 0;
  letter-spacing: 10px;
}
@media (max-width: 575.98px) {
  .leaders-club-hub .hero .copy .title {
    margin: 0 20px;
    font-size: 34px;
  }
}
@media (min-width: 768px) {
  .leaders-club-hub .hotel-benefits {
    padding: 30px 0;
  }
}
@media (min-width: 992px) {
  .leaders-club-hub .hotel-benefits {
    padding: 50px 0;
  }
}
.leaders-club-hub .hotel-benefits .benefit-details {
  padding: 30px 20px;
  font-family: "Amiri";
  font-size: 16px;
}
@media (min-width: 768px) {
  .leaders-club-hub .hotel-benefits .benefit-details {
    padding: 50px 20px;
  }
}
.leaders-club-hub .hotel-benefits .benefit-details .logo {
  padding: 0 0 20px 0;
  text-align: center;
}
.leaders-club-hub .hotel-benefits .benefit-details .logo img {
  max-height: 60px;
}
.leaders-club-hub .hotel-benefits .benefit-details .title {
  color: #192430;
}
.leaders-club-hub .hotel-benefits .benefit-details .join-and-book {
  padding-top: 20px;
  text-align: center;
}
.leaders-club-hub .hotel-benefits .benefit-details .btn-primary {
  width: 100%;
}
@media (min-width: 576px) {
  .leaders-club-hub .hotel-benefits .benefit-details .btn-primary {
    width: auto;
  }
}
.leaders-club-hub .hotel-benefits .benefit-details .benefit-list-title {
  margin-bottom: 5px;
  color: #192430;
  font-weight: bold;
}
.leaders-club-hub .hotel-benefits .benefit-details .benefit-list {
  padding-left: 25px;
}
.leaders-club-hub .hotel-benefits .benefit-details .member-login {
  padding-top: 15px;
  font-size: 14px;
}
.leaders-club-hub .hotel-benefits .mobile-benefit-image {
  width: 100%;
}
@media (min-width: 768px) {
  .leaders-club-hub .hotel-benefits .mobile-benefit-image {
    display: none;
  }
  .leaders-club-hub .hotel-benefits .split-image-wrap {
    height: 100%;
    overflow: hidden;
  }
  .leaders-club-hub .hotel-benefits .split-image-wrap .split-image {
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  .leaders-club-hub .hotel-benefits .split-image-wrap .split-image.left {
    margin-right: -100%;
  }
  .leaders-club-hub .hotel-benefits .split-image-wrap .split-image.right {
    margin-left: -100%;
  }
}
.leaders-club-hub .lc-hub-info {
  background: #ecf3fa;
}
.leaders-club-hub .lc-hub-info .copy {
  display: flex;
  flex-direction: column;
  padding: 30px 30px 40px 30px;
  font-family: "Amiri";
  font-size: 16px;
}
@media (min-width: 768px) {
  .leaders-club-hub .lc-hub-info .copy {
    padding: 40px 0 60px 0;
  }
}
.leaders-club-hub .lc-hub-info .copy .logo {
  margin: 0 auto 15px auto;
  width: 140px;
}
.leaders-club-hub .benefit-boxes {
  margin: 0 15px;
  max-width: 730px;
  font-family: "Amiri";
  font-size: 16px;
}
@media (min-width: 992px) {
  .leaders-club-hub .benefit-boxes {
    margin: 0 auto;
  }
}
.leaders-club-hub .benefit-boxes .head {
  text-align: center;
  padding-bottom: 30px;
}
.leaders-club-hub .benefit-boxes .head .title {
  display: inline-block;
  margin-top: -20px;
  padding: 60px 30px 0 30px;
  color: #192430;
  background: #fff;
  font-size: 22px;
}
.leaders-club-hub .benefit-boxes .benefit-item {
  margin: 0 3px;
  padding: 15px 20px 10px;
  height: 100%;
  border-top: 5px solid #192430;
  background: #f4f4f4;
  border-bottom: 6px solid #fff;
}
@media (min-width: 768px) {
  .leaders-club-hub .benefit-boxes .benefit-item {
    padding: 30px 20px 10px;
  }
}
.leaders-club-hub .benefit-boxes .benefit-item .benefit-icon {
  margin-bottom: 15px;
  display: flex;
  height: 50px;
}
.leaders-club-hub .benefit-boxes .benefit-item .benefit-icon .icon {
  margin: auto 0;
  max-width: 75px;
  max-height: 50px;
}
.leaders-club-hub .benefit-boxes .benefit-item h8 {
  color: #192430;
  margin-bottom: 5px;
}
.leaders-club-hub .benefit-boxes .benefit-item p {
    font-size: 13px;
  }
.leaders-club-hub .benefit-boxes .join-book-tout {
  margin: 20px 0;
  padding: 15px;
  color: #fff;
  background: #192430;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
@media (min-width: 768px) {
  .leaders-club-hub .benefit-boxes .join-book-tout {
    flex-direction: row;
  }
}
.leaders-club-hub .benefit-boxes .join-book-tout .content {
  padding: 10px;
  width: 100%;
  text-align: center;
}
@media (min-width: 576px) {
  .leaders-club-hub .benefit-boxes .join-book-tout .content {
    width: auto;
  }
}
.leaders-club-hub .benefit-boxes .join-book-tout .content p {
  margin: 0;
}
.leaders-club-hub .benefit-boxes .join-book-tout .content .btn-primary {
  width: 100%;
}
@media (min-width: 576px) {
  .leaders-club-hub .benefit-boxes .join-book-tout .content .btn-primary {
    width: auto;
  }
}
.leaders-club-hub .benefit-boxes .member-login {
  margin-left: auto;
  text-align: right;
  font-size: 14px;
  max-width: 70%;
}
@media (min-width: 768px) {
  .leaders-club-hub .benefit-boxes .member-login {
    max-width: 100%;
  }
}
.leaders-club-hub .disclaimer {
  margin: auto;
  padding: 50px 30px 0;
  max-width: 730px;
  font-size: 12px;
  line-height: normal;
  color: #999999;
}
@media (min-width: 992px) {
  .footer {
    padding: 40px 0;
  }
}
.footer .lhw-logo {
  margin-bottom: 20px;
}
.footer .lhw-logo img {
  max-width: 150px;
}
.footer .legal {
  padding: 10px 0 30px;
}
.footer .legal a:not(:last-child) {
  border-right: 1px solid #666;
}
@media (max-width: 767.98px) {
  .footer .legal {
    flex-direction: row;
    flex-wrap: wrap;
  }
  .footer .legal .copyright {
    width: 100%;
  }
  .footer .legal a {
    padding: 0;
    flex-grow: 1;
    font-size: 11px;
  }
}
