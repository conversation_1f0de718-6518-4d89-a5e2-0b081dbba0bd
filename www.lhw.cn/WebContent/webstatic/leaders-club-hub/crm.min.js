function enrollmentValidation(formId) {
  $("#Prefix", formId).rules("add", {
    required: function (element) {
      return !0
    },
  }),
      $("#FirstName", formId).rules("add", {
        required: function (element) {
          return !0
        },
        nameReg:!0
      }),
      $("#LastName", formId).rules("add", {
        required: function (element) {
          return !0
        },
        nameReg:!0
      }),
    $("#Email", formId).rules("add", {
      required: function (element) {
        return !0
      },
      complexEmail: function (element) {
        return !0
      },
      checkExisitingAccount: !0,
    }),
    $("#ConfirmEmail", formId).rules("add", {
      required: function (element) {
        return !0
      },
      equalTo: function (element) {
        return "#Email"
      },
    }),
    $("#Country", formId).rules("add", {
      required: function (element) {
        return !0
      },
    }),
    $("#City", formId).rules("add", {
      required: function (element) {
        return !0
      },
    }),
    $("#Password", formId).rules("add", {
      required: function (element) {
        return !0
      },
      passwordRequirements: function (element) {
        return !0
      },
    }),
    $("#OptinCommunicationsError", formId).rules("add", {
      required: function (element) {
        return !0
      },
    }),
    $("#AgreeToTerms", formId).rules("add", { required: !0 })
}
if (
  ((function (global, factory) {
    "object" == typeof exports && "undefined" != typeof module
      ? (module.exports = factory())
      : "function" == typeof define && define.amd
      ? define(factory)
      : ((global = global || self), (global.VueRecaptcha = factory()))
  })(this, function () {
    "use strict"
    function _extends() {
      return (_extends =
        Object.assign ||
        function (target) {
          for (var i = 1; i < arguments.length; i++) {
            var source = arguments[i]
            for (var key in source)
              Object.prototype.hasOwnProperty.call(source, key) &&
                (target[key] = source[key])
          }
          return target
        }).apply(this, arguments)
    }
    var defer = function () {
        var state = !1,
          callbacks = []
        return {
          resolved: function () {
            return state
          },
          resolve: function (val) {
            if (!state) {
              state = !0
              for (var i = 0, len = callbacks.length; i < len; i++)
                callbacks[i](val)
            }
          },
          promise: {
            then: function (cb) {
              state ? cb() : callbacks.push(cb)
            },
          },
        }
      },
      ownProp = Object.prototype.hasOwnProperty,
      recaptcha = (function () {
        var deferred = defer()
        return {
          notify: function () {
            deferred.resolve()
          },
          wait: function () {
            return deferred.promise
          },
          render: function (ele, options, cb) {
            this.wait().then(function () {
              cb(window.grecaptcha.render(ele, options))
            })
          },
          reset: function (widgetId) {
            void 0 !== widgetId &&
              (this.assertLoaded(),
              this.wait().then(function () {
                return window.grecaptcha.reset(widgetId)
              }))
          },
          execute: function (widgetId) {
            void 0 !== widgetId &&
              (this.assertLoaded(),
              this.wait().then(function () {
                return window.grecaptcha.execute(widgetId)
              }))
          },
          checkRecaptchaLoad: function () {
            ownProp.call(window, "grecaptcha") &&
              ownProp.call(window.grecaptcha, "render") &&
              this.notify()
          },
          assertLoaded: function () {
            if (!deferred.resolved())
              throw new Error("ReCAPTCHA has not been loaded")
          },
        }
      })()
    return (
      "undefined" != typeof window &&
        (window.vueRecaptchaApiLoaded = recaptcha.notify),
      {
        name: "VueRecaptcha",
        props: {
          sitekey: { type: String, required: !0 },
          theme: { type: String },
          badge: { type: String },
          type: { type: String },
          size: { type: String },
          tabindex: { type: String },
          loadRecaptchaScript: { type: Boolean, default: !1 },
          recaptchaScriptId: { type: String, default: "__RECAPTCHA_SCRIPT" },
          recaptchaHost: { type: String, default: "www.google.com" },
          language: { type: String, default: "" },
        },
        beforeMount: function () {
          if (
            this.loadRecaptchaScript &&
            !document.getElementById(this.recaptchaScriptId)
          ) {
            var script = document.createElement("script")
            ;(script.id = this.recaptchaScriptId),
              (script.src =
                "https://" +
                this.recaptchaHost +
                "/recaptcha/api.js?onload=vueRecaptchaApiLoaded&render=explicit&hl=" +
                this.language),
              (script.async = !0),
              (script.defer = !0),
              document.head.appendChild(script)
          }
        },
        mounted: function () {
          var _this = this
          recaptcha.checkRecaptchaLoad()
          var opts = _extends({}, this.$props, {
              callback: this.emitVerify,
              "expired-callback": this.emitExpired,
              "error-callback": this.emitError,
            }),
            container = this.$slots.default ? this.$el.children[0] : this.$el
          recaptcha.render(container, opts, function (id) {
            ;(_this.$widgetId = id), _this.$emit("render", id)
          })
        },
        methods: {
          reset: function () {
            recaptcha.reset(this.$widgetId)
          },
          execute: function () {
            recaptcha.execute(this.$widgetId)
          },
          emitVerify: function (response) {
            this.$emit("verify", response)
          },
          emitExpired: function () {
            this.$emit("expired")
          },
          emitError: function () {
            this.$emit("error")
          },
        },
        render: function (h) {
          return h("div", {}, this.$slots.default)
        },
      }
    )
  }),
  $("#crmActivation").length)
) {
  var crmRouter = new VueRouter({ mode: "history" })
  new Vue({
    el: "#crmActivation",
    name: "CRM Activation Form",
    router: crmRouter,
    data: {
      isSubmitting: !1,
      hasError: !1,
      accepted: !1,
      membershipNumber: null,
    },
    computed: {
      showDefaultView: function () {
        return !this.membershipNumber
      },
      showSuccessView: function () {
        return !!this.membershipNumber
      },
      showBackToBookingText: function () {
        return !this.membershipNumber && !this.isFromLCHub
      },
      showBackToBookingLink: function () {
        return !this.membershipNumber
      },
      isFromLCHub: function () {
        return (!!this.query.src && "hub" == this.query.src) || !this.query.src
      },
      query: function () {
        return this.$route.query
      },
    },
    mounted: function () {
      var self = this
      void 0 !== window.jsLHW_Mn && (self.membershipNumber = window.jsLHW_Mn)
    },
    methods: {
      onJoinRenewClick: function () {
        var self = this
        if (self.accepted && !self.isSubmitting) {
          ;(self.isSubmitting = !0), (self.hasError = !1)
          var cpd = {}
          Cookies.get("cpd").replace(
            /([^=&]+)=([^&]*)/g,
            function (m, key, value) {
              cpd[decodeURIComponent(key)] = decodeURIComponent(value)
            }
          ),
            $.ajax({
              type: "POST",
              contentType: "application/json",
              url: "/api/crm/joinrenew",
              dataType: "json",
              data: JSON.stringify({ hotel: $("#LHWHotel").val() }),
              headers: {
                __RequestCPD: cpd.a,
                __RequestVerificationToken:
                  window.getVerificationTokenCoookielessHeaders()
                    .__RequestVerificationToken,
              },
              contentType: "application/json",
              success: function (result) {
                if (
                  ((self.isSubmitting = !1),
                  result && result.data && result.data.lcMembershipNumber)
                ) {
                  self.membershipNumber = result.data.lcMembershipNumber
                  var analyticsData = window.dataLayerPageJson || {}
                  ;(analyticsData["page-data"]["view-name"] = "Confirmation"),
                    window.dataLayer.push(analyticsData)
                } else self.hasError = !0
              },
              error: function () {
                ;(self.isSubmitting = !1), (self.hasError = !0)
              },
            })
        }
      },
      onContinueBookingClick: function () {
        var self = this
        if (!self.isSubmitting) {
          ;(self.isSubmitting = !0), (this.hasError = !1)
          var cpd = {}
          Cookies.get("cpd").replace(
            /([^=&]+)=([^&]*)/g,
            function (m, key, value) {
              cpd[decodeURIComponent(key)] = decodeURIComponent(value)
            }
          ),
            $.ajax({
              type: "POST",
              contentType: "application/json",
              url: "/api/crm/signin",
              dataType: "json",
              headers: {
                __RequestCPD: cpd.a,
                __RequestVerificationToken:
                  window.getVerificationTokenCoookielessHeaders()
                    .__RequestVerificationToken,
              },
              contentType: "application/json",
              success: function (result) {
                Cookies.remove("cpd", {
                  path: "/",
                  domain: window.appData.config.cookieDomain,
                }),
                  result &&
                    result.data &&
                    result.data.accessToken &&
                    ($("#hiddenSuccessForm input[name='session']").val(
                      result.data.accessToken
                    ),
                    setTimeout(function () {
                      $("#hiddenSuccessForm").trigger("submit")
                    }, 100))
              },
              fail: function (jqXHR, textStatus, errorThrown) {
                ;(self.isSubmitting = !1), (self.hasError = !0)
              },
            })
        }
      },
    },
  })
}
if ($("#crmResetPassword").length) {
  $("#resetPasswordForm").validate({
    rules: { Email: { required: !0, email: !0 } },
  })
  var crmRouter = new VueRouter({ mode: "history" })
  new Vue({
    el: "#crmResetPassword",
    name: "CRM Reset Password Form",
    router: crmRouter,
    data: {
      isSubmitting: !1,
      hasError: !1,
      userEmail: "",
      submitSuccess: !1,
      closeDisabled: !1,
    },
    computed: {
      showDefaultView: function () {
        return !this.submitSuccess
      },
      showSuccessView: function () {
        return this.submitSuccess
      },
      showBackToBookingText: function () {
        return !this.submitSuccess && !this.isFromLCHub
      },
      showBackToBookingLink: function () {
        return !this.submitSuccess
      },
      isFromLCHub: function () {
        return (!!this.query.src && "hub" == this.query.src) || !this.query.src
      },
      query: function () {
        return this.$route.query
      },
    },
    methods: {
      onResetPasswordClick: function () {
        var self = this,
          $form = $("#resetPasswordForm"),
          isValid = $form.valid()
        if (!self.isSubmitting && isValid) {
          ;(self.isSubmitting = !0), (self.hasError = !1)
          var jsonData = { email: self.userEmail }
          $.ajax({
            type: "POST",
            contentType: "application/json",
            url: "/api/crm/forgotpassword",
            dataType: "json",
            headers: window.getVerificationTokenHeaders(),
            data: JSON.stringify(jsonData),
            xhrFields: { withCredentials: !0 },
            success: function (result) {
              ;(self.isSubmitting = !1), (self.submitSuccess = !0)
              var analyticsData = window.dataLayerPageJson || {}
              ;(analyticsData["page-data"]["view-name"] = "Confirmation")
                // window.dataLayer.push(analyticsData)
            },
            error: function (jqXHR) {
              if (((self.isSubmitting = !1), 400 == jqXHR.status)) {
                var responseError = jqXHR.responseJSON.errors,
                  errorArray = {}
                $.each(responseError, function (key, value) {
                  "model.Email" == value.id &&
                    (errorArray.Email = value.errorMessages[0])
                }),
                  $form.validate().showErrors(errorArray)
              } else self.hasError = !0
            },
          })
        }
      },
      onClosePageClick: function () {
        ;(this.closeDisabled = !0),
          window.open("", "_self", ""),
          window.close(),
          setTimeout(function () {
            $("#modalClosePage").modal()
          }, 2e3)
      },
    },
  })
}
if (
  ($.validator.addMethod(
    "checkExisitingAccount",
    function (value, element, param, method) {
      if (this.optional(element)) return "dependency-mismatch"
      method = ("string" == typeof method && method) || "remote"
      var validator,
        data,
        optionDataString,
        previous = this.previousValue(element, method)
      return (
        this.settings.messages[element.name] ||
          (this.settings.messages[element.name] = {}),
        (previous.originalMessage =
          previous.originalMessage ||
          this.settings.messages[element.name][method]),
        (this.settings.messages[element.name][method] = previous.message),
        (param = ("string" == typeof param && { url: param }) || param),
        (optionDataString = $.param($.extend({ data: value }, param.data))),
        previous.old === optionDataString
          ? previous.valid
          : ((previous.old = optionDataString),
            (validator = this),
            this.startRequest(element),
            (data = {}),
            (data[element.name] = value),
            $(
              '<div class="loading-icon small input-validating"><span></span></div>'
            ).insertBefore($("#" + $(element).attr("id"))),
            $.ajax(
              $.extend(
                !0,
                {
                  url: "/register/checkRegisterEmail?email=" + value,
                  mode: "abort",
                  type: "POST",
                  port: "validate" + element.name,
                  dataType: "json",
                  data: { email1: "" },
                  context: validator.currentForm,
                  success: function (response) {
                    var errors,
                      submitted,
                      valid = !0 === response ||  response.status == 404
                    ;(validator.settings.messages[element.name][method] =
                      previous.originalMessage),
                      valid
                        ? ((submitted = validator.formSubmitted),
                          validator.resetInternals(),
                          (validator.toHide = validator.errorsFor(element)),
                          (validator.formSubmitted = submitted),
                          validator.successList.push(element),
                          (validator.invalid[element.name] = !1),
                          validator.showErrors(),
                          $(".input-validating").remove())
                        : (((errors = {})[element.name] =
                            $(element).data("msg-remote")),
                          (validator.invalid[element.name] = !0),
                          validator.showErrors(errors),
                          $(element).blur()),
                      setTimeout(function () {
                        $(".input-validating").remove()
                      }, 1e3),
                      (previous.valid = valid),
                      validator.stopRequest(element, valid)
                  },
                  error: function () {
                    ;(errors = {}),
                      (errors[element.name] = $(element).data("msg-remote")),
                      (validator.invalid[element.name] = !0),
                      validator.showErrors(errors),
                      $(element).blur(),
                      setTimeout(function () {
                        $(".input-validating").remove()
                      }, 1e3)
                    ;(previous.valid = !1), validator.stopRequest(element, !1)
                  },
                },
                param
              )
            ),
            "pending")
      )
    }
  ),
  $("#crmEnrollment").length)
)
  var crmEnrollment = new Vue({
    el: "#crmEnrollment",
    name: "CRM Enrollment Form",
    components: { "vue-recaptcha": VueRecaptcha },
    data: {
      showBackToBookingText: !0,
      formId: "#crmEnrollmentForm",
      isValidating: !1,
      isSubmitting: !1,
      hasError: !1,
      agreedToTerms: !1,
      contact: {
        prefix: $("#Prefix").val(),
        prefixText: $("#Prefix option:selected").val(),
        firstName: $("#FirstName").val(),
        lastName: $("#LastName").val(),
        email: $("#Email").val(),
      },
      subscriptions: {
        marketingYes: {
          id: $("#OptinCommunicationsYes").data("id"),
          checked: $("#OptinCommunicationsYes").prop("checked"),
        },
        marketingNo: {
          id: $("#OptinCommunicationsNo").data("id"),
          checked: $("#OptinCommunicationsNo").prop("checked"),
        },
      },
    },
    mounted: function () {
      $('#FirstName')[0].oninput = function(e){
        var val = $(this).val();
        val = pinyinUtil.getPinyin(val,' ',false);
        val = val.slice(0,1).toUpperCase() + val.slice(1)
        $('#FirstName_en').val(val)
      }

      $('#LastName')[0].oninput = function(e){
        var val = $(this).val();
        val = pinyinUtil.getPinyin(val,' ',false);
        val = val.slice(0,1).toUpperCase() + val.slice(1)
        $('#LastName_en').val(val)
      }
      var optinCommunicationsYes = $('#OptinCommunicationsYes')
      var optinCommunicationsNo = $('#OptinCommunicationsNo')
         optinCommunicationsYes.click(function(e){
          let val = $(this).prop('checked');
          if(val){
            $(this).attr('disabled','disabled');
            if(optinCommunicationsNo.prop('checked')){
              optinCommunicationsNo.prop('checked',false);
              optinCommunicationsNo.removeAttr('disabled');
            }
          }
        })
        $('#OptinCommunicationsNo').click(function(e){
          let val = $(this).prop('checked');
          if(val){
            $(this).attr('disabled','disabled');
            if(optinCommunicationsYes.prop('checked')){
              optinCommunicationsYes.prop('checked',false);
              optinCommunicationsYes.removeAttr('disabled');
            }
          }
        })
        
        $('#AgreeToTerms').click(function(){
            console.log($(this).prop('checked'));
            if($(this).prop('checked')){
                $(".btn-submit").removeAttr("disabled")
            }else{
                $(".btn-submit").attr("disabled",'disabled')
            }
        })
        // $("#crmEnrollmentForm").validate({
        //   showErrors:function(errormap,errorlist){
        //     console.log(errorlist)
        //   },
        //   submitHandler:function (form){
        //     console.log(form)
        //       console.log(window.isverify === true)
        //       return true
        //     // if(window.isverify === true){
        //     //     return true;
        //     // }else{
        //     //     $('#validCode').show();
        //     //     return false;
        //     // }
        //   }
        // }), enrollmentValidation(this.formId)

      $("#crmEnrollmentForm").validate({
        submitHandler:function (form){
          let valuesMap = $("#crmEnrollmentForm").validate().invalid;
          setTimeout(function(){
            let isSubmit = true;
            for(var i in valuesMap){
              if(valuesMap[i]){
                isSubmit = false;
              }
            }
            if(!isSubmit){ return false}
            if(window.isverify === true){
              return true;
            }else{
              $('#validCode').show();
              return false;
            }
          },1000)

        }
      }), enrollmentValidation(this.formId)


      // 增加姓氏验证
      $.validator.addMethod("nameReg", function(value, element, param) {
        var test_reg = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]") // 是否存在特殊字符
        var reg = test_reg.test(value);
        if(reg){
          return false;
        }
        if(value.length >25){ // 字符限制  看情况
          return false;
        }
        return true;
      }, "输入格式有误，请重新输入");


    },
    computed: {
      subscriptionSelected: function () {
        return (
          !(
            !this.subscriptions.marketingYes.checked &&
            !this.subscriptions.marketingNo.checked
          ) || ""
        )
      },
    },
    methods: {
      updateSubscriptionEmails: function (key) {
        var self = this
        this.$nextTick(function () {
          key &&
            $.each(self.subscriptions, function (k, value) {
              k == key && value.checked
                ? (value.checked = !0)
                : (value.checked = !1)
            })
        })
      },
      submitEnrollment: function () {
        var self = this
        if (!self.agreedToTerms) return !1
        ;(self.isValidating = !0),
          $(self.formId).valid(),
          $(":input", self.formId).blur()
        var isFormValid = !0
        return (
          $.each($(self.formId).validate().invalid, function (key, value) {
            value && (isFormValid = !1)
          }),
          (self.isValidating = !1),
          isFormValid
            ? "" == $("#RecaptchaToken").val()
              ? (self.$refs.recaptcha.execute(), !1)
              : ((self.isSubmitting = !0),
                1)
            : (scrollToError(), !1)
        )
      },
      onContinueBookingClick: function () {
        var self = this
        if (!self.isSubmitting) {
          ;(self.isSubmitting = !0), (this.hasError = !1)
          var cpd = {}
          Cookies.get("cpd").replace(
            /([^=&]+)=([^&]*)/g,
            function (m, key, value) {
              cpd[decodeURIComponent(key)] = decodeURIComponent(value)
            }
          ),
            $.ajax({
              type: "POST",
              contentType: "application/json",
              url: "/api/crm/signin",
              dataType: "json",
              headers: {
                __RequestCPD: cpd.a,
                __RequestVerificationToken:
                  window.getVerificationTokenCoookielessHeaders()
                    .__RequestVerificationToken,
              },
              contentType: "application/json",
              success: function (result) {
                result && result.data && result.data.accessToken
                  ? (Cookies.remove("cpd", {
                      path: "/",
                      domain: window.appData.config.cookieDomain,
                    }),
                    $("#hiddenSuccessForm input[name='session']").val(
                      result.data.accessToken
                    ),
                    setTimeout(function () {
                      $("#hiddenSuccessForm").trigger("submit")
                    }, 100))
                  : ((self.isSubmitting = !1), (self.hasError = !0))
              },
              fail: function (jqXHR, textStatus, errorThrown) {
                ;(self.isSubmitting = !1), (self.hasError = !0)
              },
              error: function (jqXHR, textStatus, errorThrown) {
                ;(self.isSubmitting = !1), (self.hasError = !0)
              },
            })
        }
      },
      onCaptchaVerified: function (recaptchaToken) {
        var self = this
        self.$refs.recaptcha.reset(),
          $("#RecaptchaToken").val(recaptchaToken),
          self.submitEnrollment()
      },
      onCaptchaExpired: function () {
        this.$refs.recaptcha.reset(), $("#RecaptchaToken").val("")
      },
    },
    watch: {
      subscriptions: {
        handler: function (after, before) {
          this.$nextTick(function () {
            $("#OptinCommunicationsError").valid()
          })
        },
        deep: !0,
      },
    },
  })
var loginIds = { formId: "#loginForm" }
if (
  ($(loginIds.formId).validate({
    rules: {
      EmailInput: { required: !0, complexEmail: !0 },
      PasswordInput: { required: !0 },
    },
    submitHandler: function (form) {
      formIsSubmitting(loginIds.formId), form.submit()
    },
  }),
  $(":input", loginIds.formId).on("input", function () {
    $(".btn-submit", loginIds.formId).removeAttr("disabled")
  }),
  $("#crmActivationPOC").length)
) {
  var router = new VueRouter({ mode: "history" })
  new Vue({
    el: "#crmActivationPOC",
    name: "CRM Activation Form POC",
    router: router,
    data: {
      isSubmitting: !1,
      hasError: !1,
      accepted: !1,
      membershipNumber: null,
      addedHotelId: !1,
    },
    mounted: function () {
      var self = this
      this.$route.query.hotel ||
        ((self.addedHotelId = !0),
        self.$router.push({ query: { hotel: 6925 } }))
    },
    computed: {
      showDefaultView: function () {
        return !this.membershipNumber
      },
      showSuccessView: function () {
        return !!this.membershipNumber
      },
      showBackToBookingText: function () {
        return !this.membershipNumber
      },
    },
    methods: {
      onJoinRenewClick: function () {
        var self = this
        self.accepted &&
          !self.isSubmitting &&
          ((self.isSubmitting = !0),
          (self.hasError = !1),
          $.ajax({
            type: "POST",
            contentType: "application/json",
            url: "/api/crm/joinrenew",
            dataType: "json",
            headers: {
              __RequestCPD: Cookies.get("cpd"),
              __RequestVerificationToken:
                window.getVerificationTokenCoookielessHeaders()
                  .__RequestVerificationToken,
            },
            contentType: "application/json",
            success: function (result) {
              ;(self.isSubmitting = !1),
                result && result.data && result.data.lcMembershipNumber
                  ? (self.membershipNumber = result.data.lcMembershipNumber)
                  : (self.hasError = !0)
            },
            error: function () {
              ;(self.isSubmitting = !1), (self.hasError = !0)
            },
          }))
      },
      onContinueBookingClick: function () {
        var self = this
        self.isSubmitting ||
          ((self.isSubmitting = !0),
          (this.hasError = !1),
          $.ajax({
            type: "POST",
            contentType: "application/json",
            url: "/api/crm/signin",
            dataType: "json",
            headers: {
              __RequestCPD: Cookies.get("cpd"),
              __RequestVerificationToken:
                window.getVerificationTokenCoookielessHeaders()
                  .__RequestVerificationToken,
            },
            contentType: "application/json",
            success: function (result) {
              Cookies.remove("cpd", {
                path: "/",
                domain: window.appData.config.cookieDomain,
              }),
                result &&
                  result.data &&
                  result.data.accessToken &&
                  ($("#hiddenSuccessForm input[name='session']").val(
                    result.data.accessToken
                  ),
                  setTimeout(function () {
                    $("#hiddenSuccessForm").trigger("submit")
                  }, 100))
            },
            fail: function (jqXHR, textStatus, errorThrown) {
              ;(self.isSubmitting = !1), (self.hasError = !0)
            },
          }))
      },
    },
  })
}
