.page.login .page-title {
  letter-spacing: 0.15em;
}
.page.login .form,
.page.login .no-account {
  max-width: 320px;
  margin: 0 auto 35px auto;
}
.page.login .form .stay-logged-in,
.page.login .no-account .stay-logged-in {
  padding: 15px 0 0 0;
}
.page.login .form .buttons,
.page.login .no-account .buttons {
  padding: 20px 0 0 0;
}
.page.login .no-account h8 {
  margin: 0 0 15px 0;
  color: #192430;
  letter-spacing: 0.15em;
}
.page.login #PasswordInput-error {
  display: inline-block;
  padding-bottom: 20px;
}
@media screen and (max-width: 991px) {
  .page.login .buttons .btn {
    width: 100%;
  }
  .member-benefits .benefit-item br{
    display: none;
  }
}
.password .alert {
  text-align: center;
}
.password .form {
  max-width: 320px;
  margin: 0 auto 35px auto;
}
.password .form p strong {
  color: #232f3c;
}
.password .password-requirement {
  display: block;
  margin: 30px 0;
}
.password .password-requirement h8 {
  margin: 10px;
  color: #192430;
  letter-spacing: 0.1em;
}
.password .password-requirement ul {
  padding: 0 0 0 20px;
}
.password .buttons {
  text-align: center;
}
.password .alert-message {
  padding: 20px;
  text-align: center;
}
.crm-poc {
  margin-top: 50px;
}
.stripe {
  padding: 8px;
  background: #192430;
}
.stripe .dropdown-toggle {
  padding: 5px;
  color: #fff;
}
.stripe .dropdown-toggle:after {
  margin-left: 8px;
  margin-top: 3px;
}
.stripe .dropdown-toggle .svg-icon {
  margin-bottom: -4px;
  width: 18px;
  height: 15px;
}
.crm .redirecting {
  padding: 20px;
  text-align: center;
}
.crm .redirecting h8 {
  margin: 0 0 20px 0;
  font-size: 18px;
  letter-spacing: 0.15em;
  color: #192430;
}
.crm .redirecting .loading-icon {
  margin: auto;
}
.crm .redirecting p {
  padding: 20px;
}
.crm .back-to-booking {
  padding: 30px 20px;
  text-align: center;
}
.crm .back-to-booking .logo {
  display: block;
  margin: auto;
  margin-bottom: 25px;
  width: 100%;
  max-width: 300px;
}
.crm .back-to-booking p {
  color: #666666;
  font-family: "Amiri";
  font-size: 18px;
}
.crm .back-to-booking .btn-link {
  white-space: normal;
}
.crm .page {
  padding: 30px 20px;
}
.crm .page-header {
  margin-top: 15px;
  margin-bottom: 20px;
}
.crm .page-header .lc-logo {
  max-width: 180px;
}
.crm .page-header .page-title {
  margin: 30px 0 20px 0;
  text-transform: none;
  letter-spacing: normal;
  line-height: 1em;
  font-size: 30px;
}
.crm .page-header p {
  margin: auto;
  max-width: 460px;
  color: #666666;
  font-family: "Amiri";
  font-size: 18px;
}
.crm .page-content {
  min-height: 90vh;
  background: #f4f4f4;
}
.crm .page-content .alert-error {
  margin: 15px 0;
  display: inline-block;
}
.crm .page-content .api-error {
  color: #ea0d0d;
}
.crm .page-content .form {
  margin: auto;
  max-width: 350px;
}
.crm .page-content .form .form-group {
  text-align: left;
}
.crm .page-content .form .buttons {
  margin-top: 20px;
}
.crm .page-content .form .buttons .btn {
  white-space: normal;
}
.crm .page-content .join-lc {
  margin: auto;
  margin-top: 40px;
  padding: 25px;
  max-width: 520px;
  text-align: center;
  font-family: "Amiri";
  font-size: 18px;
}
.crm .page-content .join-lc:before {
  content: "";
  display: inline-block;
  margin: 0 auto 40px auto;
  width: 150px;
  height: 5px;
  background: #192430;
}
.crm .page-content .join-lc h4 {
  color: #192430;
  font-size: 30px;
}
.crm .page-content .join-lc a.btn {
  margin: 6px 0;
}
.crm .page-content .default-view,
.crm .page-content .success-view {
  margin: auto;
  max-width: 960px;
  text-align: center;
}
.crm .page-content .default-view .page-header,
.crm .page-content .success-view .page-header {
  margin-bottom: 20px;
}
.crm .page-content .default-view .auth-error,
.crm .page-content .success-view .auth-error {
  padding: 10px 0;
  font-weight: bold;
}
.crm .page-content .default-view .instructions,
.crm .page-content .success-view .instructions {
  padding: 10px 0;
}
.crm .page-content .default-view .instructions p,
.crm .page-content .success-view .instructions p {
  font-weight: normal;
}
.crm .page-content .default-view .custom-checkbox,
.crm .page-content .success-view .custom-checkbox {
  display: inline-block;
}
.crm .page-content .default-view .custom-checkbox label,
.crm .page-content .success-view .custom-checkbox label {
  font-family: "Amiri";
  font-size: 16px;
}
.crm .page-content .default-view .buttons,
.crm .page-content .success-view .buttons {
  margin-top: 25px;
}
@media (max-width: 575.98px) {
  .crm .page-content .default-view .btn-submit,
  .crm .page-content .success-view .btn-submit {
    width: 100%;
  }
}
.crm .page-content .default-view .benefit-boxes,
.crm .page-content .success-view .benefit-boxes {
  margin: 0 auto;
  max-width: 730px;
  font-family: "Amiri";
  font-size: 16px;
}
@media (min-width: 992px) {
  .crm .page-content .default-view .benefit-boxes,
  .crm .page-content .success-view .benefit-boxes {
    margin: 0 auto;
  }
}
.crm .page-content .default-view .benefit-boxes .head,
.crm .page-content .success-view .benefit-boxes .head {
  text-align: center;
  padding: 10px 0;
}
.crm .page-content .default-view .benefit-boxes .head .title,
.crm .page-content .success-view .benefit-boxes .head .title {
  display: inline-block;
  margin-top: -20px;
  padding: 60px 30px 0 30px;
  color: #192430;
}
.crm .page-content .default-view .benefit-boxes .benefit-item,
.crm .page-content .success-view .benefit-boxes .benefit-item {
  margin: 0 3px;
  padding: 30px 20px 10px 20px;
  height: 100%;
  text-align: left;
  border-top: 5px solid #192430;
  background: #fff;
}
.crm .page-content .default-view .benefit-boxes .benefit-item .benefit-icon,
.crm .page-content .success-view .benefit-boxes .benefit-item .benefit-icon {
  margin-bottom: 15px;
  display: flex;
  height: 50px;
}
.crm
  .page-content
  .default-view
  .benefit-boxes
  .benefit-item
  .benefit-icon
  .icon,
.crm
  .page-content
  .success-view
  .benefit-boxes
  .benefit-item
  .benefit-icon
  .icon {
  margin: auto 0;
  max-width: 75px;
  max-height: 50px;
}
.crm .page-content .default-view .benefit-boxes .benefit-item h8,
.crm .page-content .success-view .benefit-boxes .benefit-item h8 {
  color: #192430;
  margin-bottom: 5px;
}
.crm .page-content .default-view .benefit-boxes .join-book-tout,
.crm .page-content .success-view .benefit-boxes .join-book-tout {
  margin: 20px 0;
  color: #fff;
  background: #192430;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
@media (min-width: 768px) {
  .crm .page-content .default-view .benefit-boxes .join-book-tout,
  .crm .page-content .success-view .benefit-boxes .join-book-tout {
    flex-direction: row;
  }
}
.crm .page-content .default-view .benefit-boxes .join-book-tout .content,
.crm .page-content .success-view .benefit-boxes .join-book-tout .content {
  padding: 30px;
}
.crm .page-content .default-view .benefit-boxes .join-book-tout .content p,
.crm .page-content .success-view .benefit-boxes .join-book-tout .content p {
  margin: 0;
}
.crm .page-content .default-view .benefit-boxes .member-login,
.crm .page-content .success-view .benefit-boxes .member-login {
  text-align: right;
  font-size: 14px;
}
.crm .page-content .default-view .membership,
.crm .page-content .success-view .membership {
  margin: 30px;
}
.crm .page-content .default-view .membership p,
.crm .page-content .success-view .membership p {
  margin: 0;
  text-transform: uppercase;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.05em;
}
.crm .enrollment {
  margin: auto;
  max-width: 960px;
}
.crm .enrollment .page-header {
  margin-top: 50px;
  margin-bottom: 20px;
  text-align: center;
  color: #192430;
}
.crm .enrollment .page-header .logo-lc {
  max-width: 250px;
}
.crm .enrollment .member-benefits {
  margin: 30px;
  padding: 30px;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  background: #fff;
}
.crm .enrollment .member-benefits .benefit-item {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
}
@media (min-width: 576px) {
  .crm .enrollment .member-benefits .benefit-item {
    width: 50%;
  }
}
@media (min-width: 992px) {
  .crm .enrollment .member-benefits .benefit-item {
    width: 25%;
  }
}
.crm .enrollment .member-benefits .benefit-item .benefit-icon {
  margin: 0 15px;
  display: flex;
  height: 50px;
}
.crm .enrollment .member-benefits .benefit-item .benefit-icon img {
  margin: auto 0;
  width: 45px;
  max-width: 75px;
  max-height: 50px;
}
@media (min-width: 576px) {
  .crm .enrollment .member-benefits .benefit-item .benefit-icon img {
    width: auto;
    height: 30px;
  }
}
.crm .enrollment .required {
  float: right;
  font-family: "Amiri";
}
.crm .enrollment .form {
  padding: 30px;
  max-width: 100%;
}
.crm .enrollment .section .section-title {
  margin-bottom: 15px;
  color: #192430;
  font-size: 22px;
}
.crm .enrollment .section .password-requirement {
  padding: 20px 0 0 0;
}
.crm .enrollment .section .password-requirement ul {
  margin-bottom: 0;
}
.crm .enrollment .section .opt-in {
  font-family: "Amiri";
  font-size: 16px;
}
.crm .enrollment .section .custom-control-label {
  font-family: "Amiri";
  font-size: 16px;
}
.crm .enrollment .section.agree-to-terms {
  margin: auto;
  max-width: 480px;
}
.crm .enrollment .section.agree-to-terms label {
  font-family: "Amiri";
}
.crm .enrollment .section.agree-to-terms .button {
  padding: 30px 0;
  text-align: center;
}
.crm .enrollment .section.agree-to-terms .button .btn {
  width: 100%;
}
@media (min-width: 576px) {
  .crm .enrollment .section.agree-to-terms .button .btn {
    width: auto;
  }
}
.crm .enrollment .section.agree-to-terms:after {
  display: none;
}
.crm .enrollment .section:after {
  content: "";
  margin: 50px auto;
  display: block;
  width: 150px;
  height: 5px;
  background: #192430;
}
.crm .enrollment .recaptcha-text {
  text-align: center;
  font-family: "Amiri";
}
.crm .enrollment .grecaptcha-badge {
  visibility: hidden;
}
.crm .enrollment-success {
  padding: 35px 20px 50px;
}
.crm .enrollment-success .benefit-boxes {
  margin: 0 15px;
  max-width: 730px;
  font-family: "Amiri";
  font-size: 16px;
}
@media (min-width: 992px) {
  .crm .enrollment-success .benefit-boxes {
    margin: 0 auto;
  }
}
.crm .enrollment-success .benefit-boxes .head {
  text-align: center;
  padding-bottom: 30px;
}
.crm .enrollment-success .benefit-boxes .head .title {
  display: inline-block;
  margin-top: -20px;
  padding: 60px 30px 0 30px;
  color: #192430;
}
.crm .enrollment-success .benefit-boxes .benefit-item {
  margin: 0 3px;
  padding: 30px 20px 10px 20px;
  height: 100%;
  border-top: 5px solid #192430;
  background: #fff;
  border-bottom: 6px solid #fff;
}
.crm .enrollment-success .benefit-boxes .benefit-item .benefit-icon {
  margin-bottom: 15px;
  display: flex;
  height: 50px;
}
.crm .enrollment-success .benefit-boxes .benefit-item .benefit-icon .icon {
  margin: auto 0;
  max-width: 75px;
  max-height: 50px;
}
.crm .enrollment-success .benefit-boxes .benefit-item h8 {
  color: #192430;
  margin-bottom: 5px;
}
.crm.is-app-submitting:before {
  content: "";
  position: fixed;
  z-index: 1000;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.3);
  opacity: 1;
  animation-name: fadeInOpacity;
  animation-iteration-count: 1;
  animation-timing-function: ease-in;
  animation-duration: 2s;
}
@keyframes fadeInOpacity {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.validCode{
  position: fixed !important;
  bottom: 40px;
  background: #fff;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
}