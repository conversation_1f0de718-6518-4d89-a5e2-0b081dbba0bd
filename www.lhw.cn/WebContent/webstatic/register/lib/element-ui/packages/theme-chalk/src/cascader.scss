@import "mixins/mixins";
@import "common/var";
@import "./input";
@import "./popper";
@import "./tag";
@import "./cascader-panel";

@include b(cascader) {
  display: inline-block;
  position: relative;
  font-size: $--font-size-base;
  line-height: $--input-height;

  &:not(.is-disabled):hover {
    .el-input__inner {
      cursor: pointer;
      border-color: $--input-hover-border;
    }
  }

  .el-input {
    cursor: pointer;

    .el-input__inner {
      text-overflow: ellipsis;

      &:focus {
        border-color: $--input-focus-border;
      }
    }

    .el-icon-arrow-down {
      transition: transform .3s;
      font-size: 14px;

      @include when(reverse) {
        transform: rotateZ(180deg);
      }
    }

    .el-icon-circle-close:hover {
      color: $--input-clear-hover-color;
    }

    @include when(focus) {
      .el-input__inner {
        border-color: $--input-focus-border;
      }
    }
  }

  @include m(medium) {
    font-size: $--input-medium-font-size;
    line-height: $--input-medium-height;
  }

  @include m(small) {
    font-size: $--input-small-font-size;
    line-height: $--input-small-height;
  }

  @include m(mini) {
    font-size: $--input-mini-font-size;
    line-height: $--input-mini-height;
  }

  @include when(disabled) {
    .el-cascader__label {
      z-index: #{$--index-normal + 1};
      color: $--disabled-color-base;
    }
  }

  @include e(dropdown) {
    margin: 5px 0;
    font-size: $--cascader-menu-font-size;
    background: $--cascader-menu-fill;
    border: $--cascader-menu-border;
    border-radius: $--cascader-menu-radius;
    box-shadow: $--cascader-menu-shadow;
  }

  @include e(tags) {
    position: absolute;
    left: 0;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-wrap: wrap;
    line-height: normal;
    text-align: left;
    box-sizing: border-box;

    .el-tag {
      display: inline-flex;
      align-items: center;
      max-width: 100%;
      margin: 2px 0 2px 6px;
      text-overflow: ellipsis;
      background: $--cascader-tag-background;

      &:not(.is-hit) {
        border-color: transparent;
      }

      > span {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .el-icon-close {
        flex: none;
        background-color: $--color-text-placeholder;
        color: $--color-white;

        &:hover {
          background-color: $--color-text-secondary;
        }
      }
    }
  }

  @include e(suggestion-panel) {
    border-radius: $--cascader-menu-radius;
  }

  @include e(suggestion-list) {
    max-height: 204px;
    margin: 0;
    padding: 6px 0;
    font-size: $--font-size-base;
    color: $--cascader-menu-font-color;
    text-align: center;
  }

  @include e(suggestion-item) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 34px;
    padding: 0 15px;
    text-align: left;
    outline: none;
    cursor: pointer;

    &:hover, &:focus {
      background: $--cascader-node-background-hover;
    }

    &.is-checked {
      color: $--cascader-menu-selected-font-color;
      font-weight: bold;
    }

    > span {
      margin-right: 10px;
    }
  }

  @include e(empty-text) {
    margin: 10px 0;
    color: $--cascader-color-empty;
  }

  @include e(search-input) {
    flex: 1;
    height: 24px;
    min-width: 60px;
    margin: 2px 0 2px 15px;
    padding: 0;
    color: $--cascader-menu-font-color;
    border: none;
    outline: none;
    box-sizing: border-box;

    &::placeholder {
      color: $--color-text-placeholder;
    }
  }
}
