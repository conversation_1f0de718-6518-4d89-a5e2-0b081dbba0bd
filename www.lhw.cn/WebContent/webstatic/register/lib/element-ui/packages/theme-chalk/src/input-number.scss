@import "mixins/mixins";
@import "common/var";
@import "input";

@include b(input-number) {
  position: relative;
  display: inline-block;
  width: 180px;
  line-height: #{$--input-height - 2};

  .el-input {
    display: block;

    &__inner {
      -webkit-appearance: none;
      padding-left: #{$--input-height + 10};
      padding-right: #{$--input-height + 10};
      text-align: center;
    }
  }

  @include e((increase, decrease)) {
    position: absolute;
    z-index: 1;
    top: 1px;
    width: $--input-height;
    height: auto;
    text-align: center;
    background: $--background-color-base;
    color: $--color-text-regular;
    cursor: pointer;
    font-size: 13px;

    &:hover {
      color: $--color-primary;

      &:not(.is-disabled) ~ .el-input .el-input__inner:not(.is-disabled) {
        border-color: $--input-focus-border;
      }
    }

    &.is-disabled {
      color: $--disabled-color-base;
      cursor: not-allowed;
    }
  }

  @include e(increase) {
    right: 1px;
    border-radius: 0 $--border-radius-base $--border-radius-base 0;
    border-left: $--border-base;
  }

  @include e(decrease) {
    left: 1px;
    border-radius: $--border-radius-base 0 0 $--border-radius-base;
    border-right: $--border-base;
  }

  @include when(disabled) {
    @include e((increase, decrease)) {
      border-color: $--disabled-border-base;
      color: $--disabled-border-base;

      &:hover {
        color: $--disabled-border-base;
        cursor: not-allowed;
      }
    }
  }

  @include m(medium) {
    width: 200px;
    line-height: #{$--input-medium-height - 2};

    @include e((increase, decrease)) {
      width: $--input-medium-height;
      font-size: $--input-medium-font-size;
    }

    .el-input__inner {
      padding-left: #{$--input-medium-height + 7};
      padding-right: #{$--input-medium-height + 7};
    }
  }

  @include m(small) {
    width: 130px;
    line-height: #{$--input-small-height - 2};

    @include e((increase, decrease)) {
      width: $--input-small-height;
      font-size: $--input-small-font-size;

      [class*=el-icon] {
        transform: scale(.9);
      }
    }

    .el-input__inner {
      padding-left: #{$--input-small-height + 7};
      padding-right: #{$--input-small-height + 7};
    }
  }

  @include m(mini) {
    width: 130px;
    line-height: #{$--input-mini-height - 2};

    @include e((increase, decrease)) {
      width: $--input-mini-height;
      font-size: $--input-mini-font-size;

      [class*=el-icon] {
        transform: scale(.8);
      }
    }

    .el-input__inner {
      padding-left: #{$--input-mini-height + 7};
      padding-right: #{$--input-mini-height + 7};
    }
  }

  @include when(without-controls) {
    .el-input__inner {
      padding-left: 15px;
      padding-right: 15px;
    }
  }

  @include when(controls-right) {
    .el-input__inner {
      padding-left: 15px;
      padding-right: #{$--input-height + 10};
    }

    @include e((increase, decrease)) {
      height: auto;
      line-height: #{($--input-height - 2) / 2};

      [class*=el-icon] {
        transform: scale(.8);
      }
    }

    @include e(increase) {
      border-radius: 0 $--border-radius-base 0 0;
      border-bottom: $--border-base;
    }

    @include e(decrease) {
      right: 1px;
      bottom: 1px;
      top: auto;
      left: auto;
      border-right: none;
      border-left: $--border-base;
      border-radius: 0 0 $--border-radius-base 0;
    }

    &[class*=medium] {
      [class*=increase], [class*=decrease] {
        line-height: #{($--input-medium-height - 2) / 2};
      }
    }

    &[class*=small] {
      [class*=increase], [class*=decrease] {
        line-height: #{($--input-small-height - 2) / 2};
      }
    }

    &[class*=mini] {
      [class*=increase], [class*=decrease] {
        line-height: #{($--input-mini-height - 2) / 2};
      }
    }
  }
}
