{"name": "element-ui", "version": "2.15.3", "description": "A Component Library for Vue.js.", "main": "lib/element-ui.common.js", "files": ["lib", "src", "packages", "types"], "typings": "types/index.d.ts", "scripts": {"bootstrap": "yarn || npm i", "build:file": "node build/bin/iconInit.js & node build/bin/build-entry.js & node build/bin/i18n.js & node build/bin/version.js", "build:theme": "node build/bin/gen-cssfile && gulp build --gulpfile packages/theme-chalk/gulpfile.js && cp-cli packages/theme-chalk/lib lib/theme-chalk", "build:utils": "cross-env BABEL_ENV=utils babel src --out-dir lib --ignore src/index.js", "build:umd": "node build/bin/build-locale.js", "clean": "rimraf lib && rimraf packages/*/lib && rimraf test/**/coverage", "deploy:build": "npm run build:file && cross-env NODE_ENV=production webpack --config build/webpack.demo.js && echo element.eleme.io>>examples/element-ui/CNAME", "deploy:extension": "cross-env NODE_ENV=production webpack --config build/webpack.extension.js", "dev:extension": "rimraf examples/extension/dist && cross-env NODE_ENV=development webpack --watch --config build/webpack.extension.js", "dev": "npm run bootstrap && npm run build:file && cross-env NODE_ENV=development webpack-dev-server --config build/webpack.demo.js & node build/bin/template.js", "dev:play": "npm run build:file && cross-env NODE_ENV=development PLAY_ENV=true webpack-dev-server --config build/webpack.demo.js", "dist": "npm run clean && npm run build:file && npm run lint && webpack --config build/webpack.conf.js && webpack --config build/webpack.common.js && webpack --config build/webpack.component.js && npm run build:utils && npm run build:umd && npm run build:theme", "i18n": "node build/bin/i18n.js", "lint": "eslint src/**/* test/**/* packages/**/* build/**/* --quiet", "pub": "npm run bootstrap && sh build/git-release.sh && sh build/release.sh && node build/bin/gen-indices.js", "test": "npm run lint && npm run build:theme && cross-env CI_ENV=/dev/ BABEL_ENV=test karma start test/unit/karma.conf.js --single-run", "test:watch": "npm run build:theme && cross-env BABEL_ENV=test karma start test/unit/karma.conf.js"}, "faas": [{"domain": "element", "public": "temp_web/element"}, {"domain": "element-theme", "public": "examples/element-ui", "build": ["yarn", "npm run deploy:build"]}], "repository": {"type": "git", "url": "**************:ElemeFE/element.git"}, "homepage": "http://element.eleme.io", "keywords": ["eleme", "vue", "components"], "license": "MIT", "bugs": {"url": "https://github.com/ElemeFE/element/issues"}, "unpkg": "lib/index.js", "style": "lib/theme-chalk/index.css", "dependencies": {"async-validator": "~1.8.1", "babel-helper-vue-jsx-merge-props": "^2.0.0", "deepmerge": "^1.2.0", "normalize-wheel": "^1.0.1", "resize-observer-polyfill": "^1.5.0", "throttle-debounce": "^1.0.1"}, "peerDependencies": {"vue": "^2.5.17"}, "devDependencies": {"@vue/component-compiler-utils": "^2.6.0", "algoliasearch": "^3.24.5", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-loader": "^7.1.5", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-istanbul": "^4.1.1", "babel-plugin-module-resolver": "^2.2.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-2": "^6.24.1", "babel-regenerator-runtime": "^6.5.0", "chai": "^4.2.0", "chokidar": "^1.7.0", "copy-webpack-plugin": "^5.0.0", "coveralls": "^3.0.3", "cp-cli": "^1.0.2", "cross-env": "^3.1.3", "css-loader": "^2.1.0", "es6-promise": "^4.0.5", "eslint": "4.18.2", "eslint-config-elemefe": "0.1.1", "eslint-loader": "^2.0.0", "eslint-plugin-html": "^4.0.1", "eslint-plugin-json": "^1.2.0", "file-loader": "^1.1.11", "file-save": "^0.2.0", "gulp": "^4.0.0", "gulp-autoprefixer": "^6.0.0", "gulp-cssmin": "^0.2.0", "gulp-sass": "^4.0.2", "highlight.js": "^9.3.0", "html-webpack-plugin": "^3.2.0", "json-loader": "^0.5.7", "json-templater": "^1.0.4", "karma": "^4.0.1", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.2", "karma-mocha": "^1.3.0", "karma-sinon-chai": "^2.0.2", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "^0.0.32", "karma-webpack": "^3.0.5", "markdown-it": "^8.4.1", "markdown-it-anchor": "^5.0.2", "markdown-it-chain": "^1.3.0", "markdown-it-container": "^2.0.0", "mini-css-extract-plugin": "^0.4.1", "mocha": "^6.0.2", "node-sass": "^4.11.0", "optimize-css-assets-webpack-plugin": "^5.0.1", "postcss": "^7.0.14", "progress-bar-webpack-plugin": "^1.11.0", "rimraf": "^2.5.4", "sass-loader": "^7.1.0", "select-version-cli": "^0.0.2", "sinon": "^7.2.7", "sinon-chai": "^3.3.0", "style-loader": "^0.23.1", "transliteration": "^1.1.11", "uglifyjs-webpack-plugin": "^2.1.1", "uppercamelcase": "^1.1.0", "url-loader": "^1.0.1", "vue": "2.5.21", "vue-loader": "^15.7.0", "vue-router": "^3.0.1", "vue-template-compiler": "2.5.21", "vue-template-es2015-compiler": "^1.6.0", "webpack": "^4.14.0", "webpack-cli": "^3.0.8", "webpack-dev-server": "^3.1.11", "webpack-node-externals": "^1.7.2"}}