# 阿里云验证码2.0服务集成说明

## 概述
本文档说明如何在LoginController中集成阿里云验证码2.0服务，增强登录安全性。

**重要更新：** 本集成使用阿里云验证码2.0，采用最新的`VerifyIntelligentCaptcha`接口。

## 已完成的工作

### 1. 创建了阿里云验证码2.0工具类
- **文件**: `src/net/hubs1/lhw/utils/AliyunCaptcha2Util.java`
- **功能**: 封装阿里云验证码2.0验证逻辑
- **主要特性**:
  - 使用最新的验证码2.0 SDK
  - 支持配置参数注入
  - 提供客户端IP获取功能
  - 完整的错误处理和日志记录
  - 返回详细的验证结果对象

### 2. 修改了LoginController
- **文件**: `src/net/hubs1/lhw/action/LoginController.java`
- **修改内容**:
  - 添加了AliyunCaptcha2Util依赖注入
  - 在`mlogin`方法中增加验证码2.0参数（captchaVerifyParam, sceneId）
  - 在登录流程中添加验证码2.0验证逻辑
  - 新增独立的`verifyCaptcha2`接口用于验证码验证

### 3. 添加了配置参数
- **文件**: `src/config.properties`
- **新增配置**:
```properties
# 阿里云验证码2.0服务配置
aliyun.captcha.accessKeyId=${aliyun.accessKeyId}
aliyun.captcha.accessKeySecret=${aliyun.accessKeySecret}
aliyun.captcha.endpoint=captcha.cn-shanghai.aliyuncs.com
aliyun.captcha.regionId=cn-shanghai
```

### 4. 创建了测试和示例文件
- `WebContent/captcha2-test.html`: 验证码2.0测试页面
- `WebContent/aliyun-captcha-example.html`: 前端集成示例（需更新为2.0）

## 验证码2.0主要变化

### 1. 接口变更
- **旧版本**: 使用`AuthenticateSig`接口
- **新版本**: 使用`VerifyIntelligentCaptcha`接口

### 2. 参数简化
- **旧版本**: 需要sessionId、sig、token等多个参数
- **新版本**: 只需要`captchaVerifyParam`和可选的`sceneId`

### 3. SDK更新
- **旧版本**: `aliyun-java-sdk-afs`
- **新版本**: `captcha20230305` SDK

### 4. 服务地址
- **中国内地**: `captcha.cn-shanghai.aliyuncs.com`
- **海外**: `captcha.ap-southeast-1.aliyuncs.com`

## API接口说明

### 1. 登录接口 (mlogin)
**URL**: `/mlogin`  
**方法**: POST  
**参数**:
- `email`: 邮箱地址 (必填)
- `password`: 密码 (必填)
- `captchaVerifyParam`: 验证码验证参数 (可选)
- `sceneId`: 场景ID (可选，建议传入)

**验证码参数格式**:
```json
// V2架构示例
{"sceneId":"xxxxxx","certifyId":"xxxxxx","deviceToken":"xxxxxxx==","data":"xxxxxx==","..."}

// V3架构示例
eyJjZXxxxxxxxxxxxxxxnVlfQ==
```

### 2. 验证码验证接口 (verifyCaptcha2)
**URL**: `/verifyCaptcha2`  
**方法**: POST  
**参数**:
- `captchaVerifyParam`: 验证码验证参数 (必填)
- `sceneId`: 场景ID (可选，建议传入)

**返回格式**:
```json
{
  "success": true/false,
  "message": "验证结果消息",
  "code": "HTTP状态码",
  "verifyCode": "验证码（T001/F001等）",
  "verifyResult": true/false
}
```

## 需要手动完成的工作

### 1. 添加阿里云验证码2.0 SDK依赖
需要将以下JAR文件添加到`WebContent/WEB-INF/lib/`目录：

**方式一：直接下载JAR文件**
- 从阿里云OpenAPI开发者门户下载Java SDK
- 解压后将相关JAR文件复制到lib目录

**方式二：Maven依赖（如果使用Maven）**
```xml
<dependency>
    <groupId>com.aliyun</groupId>
    <artifactId>captcha20230305</artifactId>
    <version>最新版本</version>
</dependency>
```

### 2. 配置阿里云验证码参数
确保`config.properties`中的配置正确：
- AccessKey和AccessKeySecret有验证码服务权限
- Endpoint地址与服务地区匹配
- RegionId设置正确

### 3. 创建验证码场景
在阿里云验证码2.0控制台：
1. 登录阿里云控制台
2. 进入验证码2.0服务
3. 创建场景并获取场景ID
4. 配置验证策略

### 4. 前端集成
前端需要集成阿里云验证码2.0组件：
```javascript
// 示例代码（需要根据实际SDK调整）
// 初始化验证码
const captcha = new AliyunCaptcha({
    sceneId: 'YOUR_SCENE_ID',
    // 其他配置...
});

// 验证成功回调
captcha.onSuccess = function(captchaVerifyParam) {
    // 将captchaVerifyParam传递给后端
    submitLogin(email, password, captchaVerifyParam);
};
```

## 验证码返回码说明

### 成功码
- **T001**: 服务端校验通过
- **T005**: 控制台开启测试模式，且配置了验证通过

### 失败码
- **F001**: 疑似攻击请求，风险策略不通过
- **F002**: CaptchaVerifyParam参数为空
- **F003**: CaptchaVerifyParam格式不合法
- **F004**: 控制台开启测试模式，且配置了验证不通过
- **F005**: CaptchaVerifyParam中的场景ID不合法
- **F006**: 场景ID不合法，需要检查控制台配置
- **F008**: 验证数据重复提交
- **F009**: 检测到虚拟设备环境
- **F010**: 同IP访问频率超出限制
- **F011**: 同设备访问频率超出限制
- **更多错误码**: 请参考官方文档

## 测试建议

### 1. 使用测试页面
访问 `captcha2-test.html` 页面进行功能测试：
- 测试独立的验证码2.0验证接口
- 测试带验证码的登录流程
- 测试无验证码的登录流程

### 2. 测试场景
- 正确的验证码参数验证
- 错误的验证码参数验证
- 缺少验证码参数的登录
- 不同错误码的处理
- 网络异常情况处理

## 安全考虑

1. **参数完整性**: CaptchaVerifyParam由前端自动生成，服务端不能修改
2. **场景ID验证**: 建议在服务端验证场景ID，防止前端篡改
3. **IP获取**: 正确获取客户端真实IP地址
4. **错误处理**: 完善的异常处理，避免敏感信息泄露
5. **日志记录**: 详细的操作日志，便于问题排查

## 兼容性说明

1. **向后兼容**: 验证码参数是可选的，不影响现有登录功能
2. **渐进增强**: 可以逐步在需要的地方启用验证码验证
3. **配置灵活**: 通过配置文件可以灵活调整验证码服务参数

## 故障排查

如果遇到问题，请检查：
1. **SDK依赖**: 确保添加了正确的验证码2.0 SDK
2. **配置参数**: 检查AccessKey、Endpoint等配置
3. **网络连接**: 确保可以访问阿里云验证码服务
4. **场景配置**: 检查阿里云控制台的场景配置
5. **参数格式**: 确保CaptchaVerifyParam格式正确
6. **日志信息**: 查看详细的错误日志

## 下一步工作

1. 添加验证码2.0 SDK依赖
2. 在阿里云控制台创建验证码场景
3. 前端集成阿里云验证码2.0组件
4. 进行全面测试
5. 部署到测试环境验证
6. 监控运行状况并优化

## 参考资料

- [阿里云验证码2.0官方文档](https://help.aliyun.com/zh/captcha/captcha2-0/)
- [服务端接入文档](https://help.aliyun.com/zh/captcha/captcha2-0/user-guide/server-access)
- [OpenAPI开发者门户](https://api.aliyun.com/)
